#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <memory>
#include <limits>

using namespace std;

// 文件路径常量
const string USERS_FILE = "users.csv";
const string PRODUCTS_FILE = "products.csv";

// 用户基类（抽象类）
class User {
public:
    User(const string& username, const string& password, double balance)
        : username(username), password(password), balance(balance) {}

    virtual string getUserType() const = 0;

    void changePassword(const string& newPassword) {
        password = newPassword;
        saveUserInfo();
    }

    double queryBalance() const {
        return balance;
    }

    bool recharge(double amount) {
        if (amount > 0) {
            balance += amount;
            saveUserInfo();
            return true;
        }
        return false;
    }

    bool consume(double amount) {
        if (amount > 0 && amount <= balance) {
            balance -= amount;
            saveUserInfo();
            return true;
        }
        return false;
    }

    string getUserName() const {
        return username;
    }

    bool verifyPassword(const string& pwd) const {
        return password == pwd;
    }

protected:
    string username;
    string password;
    double balance;

    virtual void saveUserInfo() = 0;
};

// 消费者类
class Consumer : public User {
public:
    Consumer(const string& username, const string& password, double balance)
        : User(username, password, balance) {}

    string getUserType() const override {
        return "consumer";
    }

protected:
    void saveUserInfo() override {
        vector<vector<string>> users;
        ifstream inFile(USERS_FILE);
        string line;

        // 读取CSV文件头
        if (getline(inFile, line)) {
            users.push_back(split(line, ','));
        }

        // 读取所有用户数据
        while (getline(inFile, line)) {
            vector<string> userData = split(line, ',');
            if (userData.size() >= 4 && userData[0] == username) {
                // 更新当前用户数据
                userData[1] = password;
                userData[2] = to_string(balance);
            }
            users.push_back(userData);
        }
        inFile.close();

        // 写回CSV文件
        ofstream outFile(USERS_FILE);
        for (const auto& user : users) {
            outFile << join(user, ',') << endl;
        }
        outFile.close();
    }

private:
    // 辅助函数：分割字符串
    vector<string> split(const string& s, char delimiter) {
        vector<string> tokens;
        string token;
        istringstream tokenStream(s);
        while (getline(tokenStream, token, delimiter)) {
            tokens.push_back(token);
        }
        return tokens;
    }

    // 辅助函数：连接字符串
    string join(const vector<string>& v, char delimiter) {
        string result;
        for (size_t i = 0; i < v.size(); ++i) {
            result += v[i];
            if (i < v.size() - 1) {
                result += delimiter;
            }
        }
        return result;
    }
};

// 商家类
class Merchant : public User {
public:
    Merchant(const string& username, const string& password, double balance)
        : User(username, password, balance) {}

    string getUserType() const override {
        return "merchant";
    }

    void addProduct(const string& name, const string& description, double price, int quantity, const string& type) {
        ofstream outFile(PRODUCTS_FILE, ios::app);
        outFile << name << "," << description << "," << price << "," << quantity << "," << type << endl;
        outFile.close();
    }

    bool manageProduct(const string& name, double* price, int* quantity) {
        vector<vector<string>> products;
        ifstream inFile(PRODUCTS_FILE);
        string line;
        bool found = false;

        // 读取CSV文件头
        if (getline(inFile, line)) {
            products.push_back(split(line, ','));
        }

        // 读取所有产品数据
        while (getline(inFile, line)) {
            vector<string> productData = split(line, ',');
            if (productData.size() >= 5 && productData[0] == name) {
                // 更新产品数据
                if (price != nullptr) {
                    productData[2] = to_string(*price);
                }
                if (quantity != nullptr) {
                    productData[3] = to_string(*quantity);
                }
                found = true;
            }
            products.push_back(productData);
        }
        inFile.close();

        if (found) {
            // 写回CSV文件
            ofstream outFile(PRODUCTS_FILE);
            for (const auto& product : products) {
                outFile << join(product, ',') << endl;
            }
            outFile.close();
        }
        return found;
    }

protected:
    void saveUserInfo() override {
        vector<vector<string>> users;
        ifstream inFile(USERS_FILE);
        string line;

        // 读取CSV文件头
        if (getline(inFile, line)) {
            users.push_back(split(line, ','));
        }

        // 读取所有用户数据
        while (getline(inFile, line)) {
            vector<string> userData = split(line, ',');
            if (userData.size() >= 4 && userData[0] == username) {
                // 更新当前用户数据
                userData[1] = password;
                userData[2] = to_string(balance);
            }
            users.push_back(userData);
        }
        inFile.close();

        // 写回CSV文件
        ofstream outFile(USERS_FILE);
        for (const auto& user : users) {
            outFile << join(user, ',') << endl;
        }
        outFile.close();
    }

private:
    // 辅助函数：分割字符串
    vector<string> split(const string& s, char delimiter) {
        vector<string> tokens;
        string token;
        istringstream tokenStream(s);
        while (getline(tokenStream, token, delimiter)) {
            tokens.push_back(token);
        }
        return tokens;
    }

    // 辅助函数：连接字符串
    string join(const vector<string>& v, char delimiter) {
        string result;
        for (size_t i = 0; i < v.size(); ++i) {
            result += v[i];
            if (i < v.size() - 1) {
                result += delimiter;
            }
        }
        return result;
    }
};

// 商品基类
class Product {
public:
    Product(const string& name, const string& description, double price, int quantity)
        : name(name), description(description), price(price), quantity(quantity) {}

    virtual double getPrice() const {
        return price;
    }

    string getName() const {
        return name;
    }

    string getDescription() const {
        return description;
    }

    int getQuantity() const {
        return quantity;
    }

    void setPrice(double newPrice) {
        price = newPrice;
    }

    void setQuantity(int newQuantity) {
        quantity = newQuantity;
    }

protected:
    string name;
    string description;
    double price;
    int quantity;
};

// 图书类
class Book : public Product {
public:
    Book(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 食品类
class Food : public Product {
public:
    Food(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 日用品类
class DailyNecessities : public Product {
public:
    DailyNecessities(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 个人护理类
class PersonalCare : public Product {
public:
    PersonalCare(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 服装类
class Clothing : public Product {
public:
    Clothing(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 电器类
class Appliances : public Product {
public:
    Appliances(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 文具和体育用品类
class StationeryAndSportsGoods : public Product {
public:
    StationeryAndSportsGoods(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 玩具类
class Toys : public Product {
public:
    Toys(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 饮料类
class Beverages : public Product {
public:
    Beverages(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 母婴用品类
class MaternityAndBabyProducts : public Product {
public:
    MaternityAndBabyProducts(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 其他类
class Others : public Product {
public:
    Others(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 辅助函数：分割字符串
vector<string> split(const string& s, char delimiter) {
    vector<string> tokens;
    string token;
    istringstream tokenStream(s);
    while (getline(tokenStream, token, delimiter)) {
        tokens.push_back(token);
    }
    return tokens;
}

// 辅助函数：连接字符串
string join(const vector<string>& v, char delimiter) {
    string result;
    for (size_t i = 0; i < v.size(); ++i) {
        result += v[i];
        if (i < v.size() - 1) {
            result += delimiter;
        }
    }
    return result;
}

// 加载用户信息
vector<shared_ptr<User>> loadUsers() {
    vector<shared_ptr<User>> users;
    ifstream inFile(USERS_FILE);
    string line;

    // 跳过CSV文件头
    if (getline(inFile, line)) {
        // 文件头不处理
    }

    while (getline(inFile, line)) {
        vector<string> userData = split(line, ',');
        if (userData.size() >= 4) {
            string username = userData[0];
            string password = userData[1];
            double balance = stod(userData[2]);
            string type = userData[3];

            if (type == "consumer") {
                users.emplace_back(make_shared<Consumer>(username, password, balance));
            } else if (type == "merchant") {
                users.emplace_back(make_shared<Merchant>(username, password, balance));
            }
        }
    }
    inFile.close();
    return users;
}

// 检查密码复杂度
bool isPasswordComplex(const string& password) {
    if (password.length() < 6) {
        return false;
    }

    bool hasLetter = false;
    bool hasDigit = false;
    bool hasSymbol = false;

    for (char c : password) {
        if (isalpha(c)) {
            hasLetter = true;
        } else if (isdigit(c)) {
            hasDigit = true;
        } else {
            hasSymbol = true;
        }
    }

    return hasLetter && hasDigit && hasSymbol;
}

// 检查用户名是否已存在
bool isUsernameExists(const string& username) {
    auto users = loadUsers();
    for (const auto& user : users) {
        if (user->getUserName() == username) {
            return true;
        }
    }
    return false;
}

// 用户注册
shared_ptr<User> registerUser(const string& username, const string& password, const string& type) {
    // 检查用户名是否已存在
    if (isUsernameExists(username)) {
        cout << "This username already exists. Please choose another one." << endl;
        return nullptr;
    }

    // 检查密码复杂度
    if (!isPasswordComplex(password)) {
        cout << "Password must be at least 6 characters long and contain letters, digits, and symbols." << endl;
        return nullptr;
    }

    shared_ptr<User> newUser;
    if (type == "consumer") {
        newUser = make_shared<Consumer>(username, password, 0);
    } else if (type == "merchant") {
        newUser = make_shared<Merchant>(username, password, 0);
    } else {
        cout << "Invalid user type. Please enter 'consumer' or 'merchant'." << endl;
        return nullptr;
    }

    // 检查文件是否存在，如果不存在则创建并添加表头
    ifstream checkFile(USERS_FILE);
    if (!checkFile.good()) {
        ofstream createFile(USERS_FILE);
        createFile << "username,password,balance,type" << endl;
        createFile.close();
    } else {
        checkFile.close();
    }

    ofstream outFile(USERS_FILE, ios::app);
    outFile << username << "," << password << "," << 0 << "," << type << endl;
    outFile.close();

    cout << "Registration successful!" << endl;
    return newUser;
}

// 用户登录
shared_ptr<User> login(const string& username, const string& password) {
    auto users = loadUsers();

    for (const auto& user : users) {
        if (user->getUserName() == username) {
            if (user->verifyPassword(password)) {
                return user;
            } else {
                return nullptr;
            }
        }
    }

    return nullptr;
}

// 展示平台商品信息
void showProducts(const string& filter = "") {
    ifstream inFile(PRODUCTS_FILE);
    string line;

    // 跳过CSV文件头
    if (getline(inFile, line)) {
        // 文件头不处理
    }

    while (getline(inFile, line)) {
        vector<string> productData = split(line, ',');
        if (productData.size() >= 5) {
            string name = productData[0];
            string description = productData[1];
            double price = stod(productData[2]);
            int quantity = stoi(productData[3]);
            string type = productData[4];

            if (filter.empty() || name.find(filter) != string::npos) {
                cout << "Name: " << name << ", Description: " << description
                     << ", Price: " << price << ", Quantity: " << quantity << endl;
            }
        }
    }
    inFile.close();
}

// 同一品类商品打折
void discountProducts(const string& type, double discountRate) {
    vector<vector<string>> products;
    ifstream inFile(PRODUCTS_FILE);
    string line;

    // 读取CSV文件头
    if (getline(inFile, line)) {
        products.push_back(split(line, ','));
    }

    // 读取所有产品数据
    while (getline(inFile, line)) {
        vector<string> productData = split(line, ',');
        if (productData.size() >= 5 && productData[4] == type) {
            // 应用折扣
            double price = stod(productData[2]);
            price *= discountRate;
            productData[2] = to_string(price);
        }
        products.push_back(productData);
    }
    inFile.close();

    // 写回CSV文件
    ofstream outFile(PRODUCTS_FILE);
    for (const auto& product : products) {
        outFile << join(product, ',') << endl;
    }
    outFile.close();
}

// 初始化商品数据
void initProducts() {
    ifstream checkFile(PRODUCTS_FILE);
    if (!checkFile.good()) {
        ofstream outFile(PRODUCTS_FILE);
        outFile << "name,description,price,quantity,type" << endl;
        outFile << "C++ Primer,A classic C++ learning book,100,20,Book" << endl;
        outFile << "Effective C++,A book to improve C++ programming skills,80,15,Book" << endl;
        outFile << "The C++ Programming Language,The authoritative C++ language book,120,10,Book" << endl;
        outFile << "T-Shirt,A simple white T-shirt,50,30,Clothing" << endl;
        outFile << "Jeans,Blue jeans,150,25,Clothing" << endl;
        outFile << "Sweater,A gray sweater,120,20,Clothing" << endl;
        outFile << "Chocolate,Rich chocolate,20,50,Food" << endl;
        outFile << "Biscuit,Delicious biscuits,10,60,Food" << endl;
        outFile << "Candy,Fruit hard candies,5,80,Food" << endl;
        outFile << "Toothpaste,Mint flavor toothpaste,15,40,Personal Care" << endl;
        outFile << "Shampoo,Moisturizing shampoo,25,35,Personal Care" << endl;
        outFile << "Soap,Antibacterial soap,8,70,Personal Care" << endl;
        outFile << "Toilet Paper,Soft toilet paper,12,100,Daily Necessities" << endl;
        outFile << "Detergent,Powerful cleaning detergent,30,45,Daily Necessities" << endl;
        outFile << "Refrigerator,Energy-efficient refrigerator,2000,5,Appliances" << endl;
        outFile << "Microwave,Compact microwave oven,500,8,Appliances" << endl;
        outFile << "Notebook,Lined notebook,5,200,Stationery and Sports Goods" << endl;
        outFile << "Basketball,Standard size basketball,60,15,Stationery and Sports Goods" << endl;
        outFile << "Teddy Bear,Soft plush teddy bear,40,25,Toys" << endl;
        outFile << "Building Blocks,Educational building blocks,35,30,Toys" << endl;
        outFile << "Cola,Refreshing cola drink,3,150,Beverages" << endl;
        outFile << "Orange Juice,Fresh orange juice,4,120,Beverages" << endl;
        outFile << "Baby Diapers,Absorbent baby diapers,50,40,Maternity and Baby Products" << endl;
        outFile << "Baby Formula,Nutritious baby formula,80,30,Maternity and Baby Products" << endl;
        outFile << "Gift Card,Redeemable gift card,100,50,Others" << endl;
        outFile.close();
    }
    checkFile.close();
}

// 前向声明
void showUserMenu(shared_ptr<User> currentUser);
void showMainMenu();
void registerScreen();
void loginScreen();
void displayProductsScreen(shared_ptr<User> currentUser = nullptr);
void searchProductsScreen(shared_ptr<User> currentUser = nullptr);
void changePasswordScreen(shared_ptr<User> currentUser);
void checkBalanceScreen(shared_ptr<User> currentUser);
void rechargeScreen(shared_ptr<User> currentUser);
void makePurchaseScreen(shared_ptr<User> currentUser);
void addProductScreen(shared_ptr<User> currentUser);
void manageProductsScreen(shared_ptr<User> currentUser);
void applyDiscountScreen(shared_ptr<User> currentUser);

// 显示主菜单并处理选择
void showMainMenu() {
    int choice = 0;

    system("cls");  // 清屏，Windows系统使用
    // system("clear");  // 清屏，Linux/Mac系统使用
    cout << "\n===== E-Commerce Platform =====\n";
    cout << "1. Register\n";
    cout << "2. Login\n";
    cout << "3. Display Products\n";
    cout << "4. Search Products\n";
    cout << "5. Exit\n";
    cout << "Please enter your choice: ";

    // 获取用户输入
    cin >> choice;
    if (cin.fail()) {
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        showMainMenu();  // 重新显示菜单
        return;
    }
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    // 处理用户选择
    switch (choice) {
        case 1:
            registerScreen();
            break;
        case 2:
            loginScreen();
            break;
        case 3:
            displayProductsScreen();
            break;
        case 4:
            searchProductsScreen();
            break;
        case 5:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Please try again." << endl;
            cout << "\nPress Enter to continue...";
            cin.get();
            showMainMenu();
    }
}

// 注册界面
void registerScreen() {
    system("cls");
    cout << "\n===== Register =====\n";

    string username, password, type;

    // 获取用户名并检查是否已存在
    while (true) {
        cout << "Please enter your username: ";
        getline(cin, username);

        if (isUsernameExists(username)) {
            cout << "This username already exists. Would you like to:" << endl;
            cout << "1. Try another username" << endl;
            cout << "2. Go to login page" << endl;
            cout << "3. Return to main menu" << endl;
            cout << "Please choose: ";

            int choice;
            cin >> choice;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            switch (choice) {
                case 1:
                    // 继续循环，重新输入用户名
                    break;
                case 2:
                    // 跳转到登录界面
                    loginScreen();
                    return;
                case 3:
                    // 返回主界面
                    showMainMenu();
                    return;
                default:
                    cout << "Invalid choice, defaulting to try another username." << endl;
                    break;
            }
        } else {
            break;
        }
    }

    // 获取密码并检查复杂度
    while (true) {
        cout << "Please enter your password (at least 6 characters, must include letters, digits, and symbols): ";
        getline(cin, password);

        if (!isPasswordComplex(password)) {
            cout << "Password is not complex enough. Please try again." << endl;
        } else {
            break;
        }
    }

    // 获取用户类型
    while (true) {
        cout << "Please enter your user type (consumer/merchant): ";
        getline(cin, type);

        if (type != "consumer" && type != "merchant") {
            cout << "Invalid user type. Please enter 'consumer' or 'merchant'." << endl;
        } else {
            break;
        }
    }

    shared_ptr<User> currentUser = registerUser(username, password, type);
    if (currentUser) {
        cout << "Press Enter to continue...";
        cin.get();
        showUserMenu(currentUser);
    } else {
        cout << "Registration failed. Press Enter to return to main menu...";
        cin.get();
        showMainMenu();
    }
}

// 登录界面
void loginScreen() {
    system("cls");
    cout << "\n===== Login =====\n";

    string username, password;
    shared_ptr<User> currentUser = nullptr;
    bool usernameValid = false;

    // 获取并验证用户名
    while (!usernameValid) {
        cout << "Please enter your username: ";
        getline(cin, username);

        // 检查用户名是否存在
        auto users = loadUsers();
        for (const auto& user : users) {
            if (user->getUserName() == username) {
                usernameValid = true;
                break;
            }
        }

        if (!usernameValid) {
            cout << "Username not found. Would you like to:" << endl;
            cout << "1. Try another username" << endl;
            cout << "2. Go to registration page" << endl;
            cout << "Please choose: ";

            int choice;
            cin >> choice;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            switch (choice) {
                case 1:
                    // 继续循环，重新输入用户名
                    break;
                case 2:
                    // 跳转到注册界面
                    registerScreen();
                    return;
                default:
                    cout << "Invalid choice, defaulting to try another username." << endl;
                    break;
            }
        }
    }

    // 获取并验证密码
    bool passwordValid = false;
    while (!passwordValid) {
        cout << "Please enter your password: ";
        getline(cin, password);

        currentUser = login(username, password);
        if (currentUser) {
            passwordValid = true;
        } else {
            cout << "Incorrect password. Please try again." << endl;
        }
    }

    showUserMenu(currentUser);
}

// 显示商品界面
void displayProductsScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Products =====\n";
    cout << "1. View all products\n";

    // 明确检查currentUser是否为空
    if (currentUser) {
        cout << "2. Return to user menu\n";
    } else {
        cout << "2. Return to main menu\n";
    }

    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续显示商品流程
        case 2:
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to previous menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
            return;
    }

    // 显示所有商品
    cout << "\nAll available products:\n";
    showProducts();

    cout << "\n1. Search for specific products\n";

    // 同样明确检查currentUser
    if (currentUser) {
        cout << "2. Return to user menu\n";
    } else {
        cout << "2. Return to main menu\n";
    }

    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            searchProductsScreen(currentUser);
            break;
        case 2:
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to previous menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
    }
}

// 搜索商品界面
void searchProductsScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Search Products =====\n";

    cout << "Please enter the search keyword (or type 'back' to return): ";
    string keyword;
    getline(cin, keyword);

    if (keyword == "back") {
        if (currentUser) {
            showUserMenu(currentUser);
        } else {
            showMainMenu();
        }
        return;
    }

    cout << "\nSearch results for '" << keyword << "':\n";
    showProducts(keyword);

    cout << "\n1. Search again\n";
    cout << "2. Return to " << (currentUser ? "user menu" : "main menu") << "\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            searchProductsScreen(currentUser);
            break;
        case 2:
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to previous menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
    }
}

// 显示用户菜单并处理选择
void showUserMenu(shared_ptr<User> currentUser) {
    int choice = 0;

    system("cls");  // 清屏
    cout << "\n===== User Menu =====\n";
    cout << "Welcome, " << currentUser->getUserName() << " (" << currentUser->getUserType() << ")!\n\n";

    if (currentUser->getUserType() == "consumer") {
        cout << "1. Change Password\n";
        cout << "2. Check Balance\n";
        cout << "3. Recharge\n";
        cout << "4. Make Purchase\n";
        cout << "5. Display Products\n";
        cout << "6. Search Products\n";
        cout << "7. Logout\n";
        cout << "8. Exit\n";
    } else if (currentUser->getUserType() == "merchant") {
        cout << "1. Change Password\n";
        cout << "2. Check Balance\n";
        cout << "3. Recharge\n";
        cout << "4. Add Product\n";
        cout << "5. Manage Products\n";
        cout << "6. Display Products\n";
        cout << "7. Search Products\n";
        cout << "8. Apply Discount\n";
        cout << "9. Logout\n";
        cout << "10. Exit\n";
    }
    cout << "Please enter your choice: ";

    // 获取用户输入
    cin >> choice;
    if (cin.fail()) {
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        showUserMenu(currentUser);  // 重新显示菜单
        return;
    }
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    // 处理用户选择
    if (currentUser->getUserType() == "consumer") {
        switch (choice) {
            case 1:
                changePasswordScreen(currentUser);
                break;
            case 2:
                checkBalanceScreen(currentUser);
                break;
            case 3:
                rechargeScreen(currentUser);
                break;
            case 4:
                makePurchaseScreen(currentUser);
                break;
            case 5:
                displayProductsScreen(currentUser);  // 传递currentUser参数
                break;
            case 6:
                searchProductsScreen(currentUser);
                break;
            case 7:
                showMainMenu();
                break;
            case 8:
                cout << "Thank you for using our platform. Goodbye!" << endl;
                exit(0);
            default:
                cout << "Invalid choice. Please try again." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                showUserMenu(currentUser);
        }
    } else if (currentUser->getUserType() == "merchant") {
        switch (choice) {
            case 1:
                changePasswordScreen(currentUser);
                break;
            case 2:
                checkBalanceScreen(currentUser);
                break;
            case 3:
                rechargeScreen(currentUser);
                break;
            case 4:
                addProductScreen(currentUser);
                break;
            case 5:
                manageProductsScreen(currentUser);
                break;
            case 6:
                displayProductsScreen(currentUser);  // 传递currentUser参数
                break;
            case 7:
                searchProductsScreen(currentUser);
                break;
            case 8:
                applyDiscountScreen(currentUser);
                break;
            case 9:
                showMainMenu();
                break;
            case 10:
                cout << "Thank you for using our platform. Goodbye!" << endl;
                exit(0);
            default:
                cout << "Invalid choice. Please try again." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                showUserMenu(currentUser);
        }
    }
}

// 修改密码界面
void changePasswordScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Change Password =====\n";

    string newPassword;

    while (true) {
        cout << "Please enter your new password (at least 6 characters, must include letters, digits, and symbols)\n";
        cout << "Or type 'exit' to cancel: ";
        getline(cin, newPassword);

        if (newPassword == "exit") {
            cout << "Password change cancelled." << endl;
            cout << "Press Enter to return to user menu...";
            cin.get();
            showUserMenu(currentUser);
            return;
        }

        if (!isPasswordComplex(newPassword)) {
            cout << "Password is not complex enough. Please try again." << endl;
        } else {
            break;
        }
    }

    currentUser->changePassword(newPassword);
    cout << "Password changed successfully!" << endl;

    cout << "\n1. Return to user menu\n";
    cout << "2. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            showUserMenu(currentUser);
            break;
        case 2:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 查询余额界面
void checkBalanceScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Check Balance =====\n";

    cout << "Current balance: " << currentUser->queryBalance() << endl;

    cout << "\n1. Return to user menu\n";
    cout << "2. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            showUserMenu(currentUser);
            break;
        case 2:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 充值界面
void rechargeScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Recharge =====\n";
    cout << "1. Proceed with recharge\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续充值流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    double amount;
    cout << "Please enter the recharge amount: ";
    cin >> amount;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    if (currentUser->recharge(amount)) {
        cout << "Recharge successful!" << endl;
    } else {
        cout << "Invalid recharge amount. Please enter a positive number." << endl;
    }

    cout << "\n1. Recharge again\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            rechargeScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 消费者购买界面
void makePurchaseScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Make Purchase =====\n";
    cout << "1. Proceed with purchase\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续购买流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    if (currentUser->getUserType() == "consumer") {
        string productName;
        int quantity;

        // 显示所有商品
        cout << "Available products:\n";
        showProducts();

        // 获取商品名称
        cout << "\nPlease enter the product name you want to purchase (or type 'back' to return): ";
        getline(cin, productName);

        if (productName == "back") {
            showUserMenu(currentUser);
            return;
        }

        // 获取购买数量
        cout << "Please enter the quantity: ";
        cin >> quantity;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        // 查找商品并计算价格
        ifstream inFile(PRODUCTS_FILE);
        string line;
        bool productFound = false;
        double totalPrice = 0;
        int availableQuantity = 0;
        string productType;

        // 跳过CSV文件头
        if (getline(inFile, line)) {
            // 文件头不处理
        }

        // 查找商品
        while (getline(inFile, line)) {
            vector<string> productData = split(line, ',');
            if (productData.size() >= 5 && productData[0] == productName) {
                double price = stod(productData[2]);
                availableQuantity = stoi(productData[3]);
                productType = productData[4];

                if (quantity <= 0) {
                    cout << "Invalid quantity. Please enter a positive number." << endl;
                    inFile.close();

                    cout << "\n1. Try again\n";
                    cout << "2. Return to user menu\n";
                    cout << "3. Exit\n";
                    cout << "Please enter your choice: ";

                    int choice;
                    cin >> choice;
                    cin.ignore(numeric_limits<streamsize>::max(), '\n');

                    switch (choice) {
                        case 1:
                            makePurchaseScreen(currentUser);
                            return;
                        case 2:
                            showUserMenu(currentUser);
                            return;
                        case 3:
                            cout << "Thank you for using our platform. Goodbye!" << endl;
                            exit(0);
                        default:
                            showUserMenu(currentUser);
                            return;
                    }
                }

                if (quantity > availableQuantity) {
                    cout << "Not enough stock. Available quantity: " << availableQuantity << endl;
                    inFile.close();

                    cout << "\n1. Try again\n";
                    cout << "2. Return to user menu\n";
                    cout << "3. Exit\n";
                    cout << "Please enter your choice: ";

                    int choice;
                    cin >> choice;
                    cin.ignore(numeric_limits<streamsize>::max(), '\n');

                    switch (choice) {
                        case 1:
                            makePurchaseScreen(currentUser);
                            return;
                        case 2:
                            showUserMenu(currentUser);
                            return;
                        case 3:
                            cout << "Thank you for using our platform. Goodbye!" << endl;
                            exit(0);
                        default:
                            showUserMenu(currentUser);
                            return;
                    }
                }

                totalPrice = price * quantity;
                productFound = true;
                break;
            }
        }
        inFile.close();

        if (!productFound) {
            cout << "Product not found." << endl;

            cout << "\n1. Try again\n";
            cout << "2. Return to user menu\n";
            cout << "3. Exit\n";
            cout << "Please enter your choice: ";

            int choice;
            cin >> choice;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            switch (choice) {
                case 1:
                    makePurchaseScreen(currentUser);
                    return;
                case 2:
                    showUserMenu(currentUser);
                    return;
                case 3:
                    cout << "Thank you for using our platform. Goodbye!" << endl;
                    exit(0);
                default:
                    showUserMenu(currentUser);
                    return;
            }
        }

        // 检查余额并扣款
        if (currentUser->consume(totalPrice)) {
            // 更新商品库存
            vector<vector<string>> products;
            ifstream inFile2(PRODUCTS_FILE);

            // 读取CSV文件头
            if (getline(inFile2, line)) {
                products.push_back(split(line, ','));
            }

            // 读取所有产品数据
            while (getline(inFile2, line)) {
                vector<string> productData = split(line, ',');
                if (productData.size() >= 5 && productData[0] == productName) {
                    // 更新库存
                    int newQuantity = availableQuantity - quantity;
                    productData[3] = to_string(newQuantity);
                }
                products.push_back(productData);
            }
            inFile2.close();

            // 写回CSV文件
            ofstream outFile(PRODUCTS_FILE);
            for (const auto& product : products) {
                outFile << join(product, ',') << endl;
            }
            outFile.close();

            cout << "Purchase successful!" << endl;
            cout << "Total cost: " << totalPrice << endl;
            cout << "Remaining balance: " << currentUser->queryBalance() << endl;
        } else {
            cout << "Insufficient balance. Your balance: " << currentUser->queryBalance()
                 << ", Required: " << totalPrice << endl;
        }
    }

    cout << "\n1. Make another purchase\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            makePurchaseScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 商家添加商品界面
void addProductScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Add Product =====\n";
    cout << "1. Proceed with adding product\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续添加商品流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    if (currentUser->getUserType() == "merchant") {
        string name, description, type;
        double price;
        int quantity;

        cout << "Please enter product name (or type 'back' to return): ";
        getline(cin, name);

        if (name == "back") {
            showUserMenu(currentUser);
            return;
        }

        cout << "Please enter product description: ";
        getline(cin, description);
        cout << "Please enter product price: ";
        cin >> price;
        cout << "Please enter product quantity: ";
        cin >> quantity;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        cout << "Available product types:" << endl;
        cout << "1. Book" << endl;
        cout << "2. Food" << endl;
        cout << "3. Daily Necessities" << endl;
        cout << "4. Personal Care" << endl;
        cout << "5. Clothing" << endl;
        cout << "6. Appliances" << endl;
        cout << "7. Stationery and Sports Goods" << endl;
        cout << "8. Toys" << endl;
        cout << "9. Beverages" << endl;
        cout << "10. Maternity and Baby Products" << endl;
        cout << "11. Others" << endl;
        cout << "Please enter product type number: ";

        int typeChoice;
        cin >> typeChoice;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        switch (typeChoice) {
            case 1: type = "Book"; break;
            case 2: type = "Food"; break;
            case 3: type = "Daily Necessities"; break;
            case 4: type = "Personal Care"; break;
            case 5: type = "Clothing"; break;
            case 6: type = "Appliances"; break;
            case 7: type = "Stationery and Sports Goods"; break;
            case 8: type = "Toys"; break;
            case 9: type = "Beverages"; break;
            case 10: type = "Maternity and Baby Products"; break;
            case 11: type = "Others"; break;
            default:
                cout << "Invalid choice. Using 'Others' as default." << endl;
                type = "Others";
        }

        auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
        if (merchant) {
            merchant->addProduct(name, description, price, quantity, type);
            cout << "Product added successfully!" << endl;
        }
    }

    cout << "\n1. Add another product\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            addProductScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 商家管理商品界面
void manageProductsScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Manage Products =====\n";
    cout << "1. Proceed with managing products\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续管理商品流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    if (currentUser->getUserType() == "merchant") {
        string name;
        double price;
        int quantity;

        cout << "Please enter product name to manage (or type 'back' to return): ";
        getline(cin, name);

        if (name == "back") {
            showUserMenu(currentUser);
            return;
        }

        cout << "Please enter new price (0 to keep current): ";
        cin >> price;
        cout << "Please enter new quantity (0 to keep current): ";
        cin >> quantity;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
        if (merchant) {
            double* pricePtr = price > 0 ? &price : nullptr;
            int* quantityPtr = quantity > 0 ? &quantity : nullptr;

            if (merchant->manageProduct(name, pricePtr, quantityPtr)) {
                cout << "Product updated successfully!" << endl;
            } else {
                cout << "Product not found!" << endl;
            }
        }
    }

    cout << "\n1. Manage another product\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            manageProductsScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 商家打折界面
void applyDiscountScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Apply Discount =====\n";
    cout << "1. Proceed with applying discount\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续打折流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    if (currentUser->getUserType() == "merchant") {
        cout << "Available product types:" << endl;
        cout << "1. Book" << endl;
        cout << "2. Food" << endl;
        cout << "3. Daily Necessities" << endl;
        cout << "4. Personal Care" << endl;
        cout << "5. Clothing" << endl;
        cout << "6. Appliances" << endl;
        cout << "7. Stationery and Sports Goods" << endl;
        cout << "8. Toys" << endl;
        cout << "9. Beverages" << endl;
        cout << "10. Maternity and Baby Products" << endl;
        cout << "11. Others" << endl;
        cout << "12. Return to user menu" << endl;
        cout << "Please enter product type number to discount: ";

        int typeChoice;
        cin >> typeChoice;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        if (typeChoice == 12) {
            showUserMenu(currentUser);
            return;
        }

        string type;
        switch (typeChoice) {
            case 1: type = "Book"; break;
            case 2: type = "Food"; break;
            case 3: type = "Daily Necessities"; break;
            case 4: type = "Personal Care"; break;
            case 5: type = "Clothing"; break;
            case 6: type = "Appliances"; break;
            case 7: type = "Stationery and Sports Goods"; break;
            case 8: type = "Toys"; break;
            case 9: type = "Beverages"; break;
            case 10: type = "Maternity and Baby Products"; break;
            case 11: type = "Others"; break;
            default:
                cout << "Invalid choice. Operation cancelled." << endl;
                cout << "Press Enter to return to user menu...";
                cin.get();
                showUserMenu(currentUser);
                return;
        }

        double rate;
        cout << "Please enter discount rate (0.1-1.0): ";
        cin >> rate;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        if (rate > 0 && rate <= 1) {
            discountProducts(type, rate);
            cout << "Discount applied successfully!" << endl;
        } else {
            cout << "Invalid discount rate!" << endl;
        }
    }

    cout << "\n1. Apply another discount\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            applyDiscountScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

int main() {
    initProducts();
    showMainMenu();
    return 0;
}


