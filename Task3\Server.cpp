#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <memory>
#include <limits>
#include <map>
#include <ctime>
#include <iomanip>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <mingw.thread.h>
#include <mingw.mutex.h>
#include <atomic>

#pragma comment(lib, "ws2_32.lib")

using namespace std;

// 文件路径常量
const string USERS_FILE = "users.csv";
const string PRODUCTS_FILE = "products.csv";

// 服务器配置
const int PORT = 8888;
const int BUFFER_SIZE = 4096;

// 互斥锁，用于保护共享资源
std::mutex fileMutex;
std::atomic<bool> serverRunning(true);

// 从Transaction_Management_Subsystem.cpp导入的函数声明
vector<string> split(const string& s, char delimiter);
string join(const vector<string>& v, char delimiter);
vector<shared_ptr<class User>> loadUsers();
bool isUsernameExists(const string& username);
bool isPasswordComplex(const string& password);
shared_ptr<class User> registerUser(const string& username, const string& password, const string& type);
shared_ptr<class User> login(const string& username, const string& password);
void showProducts(const string& filter);
void discountProducts(const string& type, double discountRate);
void initProducts();

// 辅助函数：解析带引号的参数
vector<string> parseQuotedParams(const string& input) {
    vector<string> params;
    string current = "";
    bool inQuotes = false;
    bool escapeNext = false;

    for (size_t i = 0; i < input.length(); i++) {
        char c = input[i];

        if (escapeNext) {
            current += c;
            escapeNext = false;
        } else if (c == '\\') {
            escapeNext = true;
        } else if (c == '"') {
            inQuotes = !inQuotes;
        } else if (c == ' ' && !inQuotes) {
            if (!current.empty()) {
                params.push_back(current);
                current = "";
            }
        } else {
            current += c;
        }
    }

    if (!current.empty()) {
        params.push_back(current);
    }

    return params;
}

// 用户基类（抽象类）
class User {
public:
    User(const string& username, const string& password, double balance)
        : username(username), password(password), balance(balance) {}

    virtual string getUserType() const = 0;

    void changePassword(const string& newPassword) {
        password = newPassword;
        saveUserInfo();
    }

    double queryBalance() const {
        return balance;
    }

    bool recharge(double amount) {
        if (amount > 0) {
            balance += amount;
            saveUserInfo();
            return true;
        }
        return false;
    }

    bool consume(double amount) {
        if (amount > 0 && amount <= balance) {
            balance -= amount;
            saveUserInfo();
            return true;
        }
        return false;
    }

    string getUserName() const {
        return username;
    }

    bool verifyPassword(const string& pwd) const {
        return password == pwd;
    }

protected:
    string username;
    string password;
    double balance;

    virtual void saveUserInfo() = 0;
};

// 消费者类
class Consumer : public User {
public:
    Consumer(const string& username, const string& password, double balance)
        : User(username, password, balance) {}

    string getUserType() const override {
        return "consumer";
    }

protected:
    void saveUserInfo() override {
        std::lock_guard<std::mutex> lock(fileMutex); // 加锁保护文件操作
        vector<vector<string>> users;
        ifstream inFile(USERS_FILE);
        string line;

        // 读取CSV文件头
        if (getline(inFile, line)) {
            users.push_back(split(line, ','));
        }

        // 读取所有用户数据
        while (getline(inFile, line)) {
            vector<string> userData = split(line, ',');
            if (userData.size() >= 4 && userData[0] == username) {
                // 更新当前用户数据
                userData[1] = password;
                userData[2] = to_string(balance);
            }
            users.push_back(userData);
        }
        inFile.close();

        // 写回CSV文件
        ofstream outFile(USERS_FILE);
        for (const auto& user : users) {
            outFile << join(user, ',') << endl;
        }
        outFile.close();
    }
};

// 商家类
class Merchant : public User {
public:
    Merchant(const string& username, const string& password, double balance)
        : User(username, password, balance) {}

    string getUserType() const override {
        return "merchant";
    }

    void addProduct(const string& name, const string& description, double price, int quantity, const string& type);
    bool manageProduct(const string& name, double* price, int* quantity);

    // 接收订单
    void receiveOrder(const string& orderId, const string& consumer, const string& date, double amount);

    // 更新订单状态
    void updateOrderStatus(const string& orderId, const string& newStatus);

protected:
    void saveUserInfo() override {
        std::lock_guard<std::mutex> lock(fileMutex); // 加锁保护文件操作
        vector<vector<string>> users;
        ifstream inFile(USERS_FILE);
        string line;

        // 读取CSV文件头
        if (getline(inFile, line)) {
            users.push_back(split(line, ','));
        }

        // 读取所有用户数据
        while (getline(inFile, line)) {
            vector<string> userData = split(line, ',');
            if (userData.size() >= 4 && userData[0] == username) {
                // 更新当前用户数据
                userData[1] = password;
                userData[2] = to_string(balance);
            }
            users.push_back(userData);
        }
        inFile.close();

        // 写回CSV文件
        ofstream outFile(USERS_FILE);
        for (const auto& user : users) {
            outFile << join(user, ',') << endl;
        }
        outFile.close();
    }
};

// 处理客户端请求的函数
void handleClient(SOCKET clientSocket) {
    char buffer[BUFFER_SIZE];
    shared_ptr<User> currentUser = nullptr;
    bool clientConnected = true;

    while (clientConnected && serverRunning) {
        // 接收客户端请求
        memset(buffer, 0, BUFFER_SIZE);
        int bytesReceived = recv(clientSocket, buffer, BUFFER_SIZE, 0);

        if (bytesReceived <= 0) {
            // 客户端断开连接或发生错误
            clientConnected = false;
            break;
        }

        string request(buffer);

        // 输出用户请求（但不显示密码）
        string logRequest = request;
        if (logRequest.find("LOGIN") == 0 || logRequest.find("REGISTER") == 0 ||
            logRequest.find("CHANGE_PASSWORD") == 0 || logRequest.find("PAY_ORDER") == 0) {
            // 对于包含密码的请求，隐藏密码部分
            size_t firstSpace = logRequest.find(' ');
            if (firstSpace != string::npos) {
                size_t secondSpace = logRequest.find(' ', firstSpace + 1);
                if (secondSpace != string::npos) {
                    logRequest = logRequest.substr(0, secondSpace) + " ********";
                }
            }
        }
        cout << "Received request: " << logRequest << endl;

        string response;

        // 解析请求
        istringstream iss(request);
        string command;
        iss >> command;

        if (command == "LOGIN") {
            // 登录请求: LOGIN username password
            string username, password;
            iss >> username >> password;

            currentUser = login(username, password);
            if (currentUser) {
                response = "LOGIN_SUCCESS " + currentUser->getUserType() + " " + currentUser->getUserName();
            } else {
                response = "LOGIN_FAILED";
            }
        }
        else if (command == "REGISTER") {
            // 注册请求: REGISTER username password type
            string username, password, type;
            iss >> username >> password >> type;

            if (isUsernameExists(username)) {
                response = "REGISTER_FAILED Username already exists";
            }
            else if (!isPasswordComplex(password)) {
                response = "REGISTER_FAILED Password not complex enough";
            }
            else {
                currentUser = registerUser(username, password, type);
                if (currentUser) {
                    response = "REGISTER_SUCCESS";
                } else {
                    response = "REGISTER_FAILED Unknown error";
                }
            }
        }
        else if (command == "CHECK_USERNAME") {
            // 检查用户名是否存在: CHECK_USERNAME username
            string username;
            iss >> username;

            if (isUsernameExists(username)) {
                response = "USERNAME_EXISTS";
            } else {
                response = "USERNAME_AVAILABLE";
            }
        }
        else if (command == "GET_PRODUCTS") {
            // 获取所有商品: GET_PRODUCTS [filter]
            string filter = "";
            string rest;
            getline(iss, rest);

            // 安全地处理前导空格
            if (!rest.empty() && rest[0] == ' ') {
                filter = rest.substr(1); // 跳过前导空格
            } else if (!rest.empty()) {
                filter = rest; // 没有前导空格，直接使用
            }

            // 直接读取CSV文件并返回原始数据
            std::lock_guard<std::mutex> lock(fileMutex);
            ifstream inFile(PRODUCTS_FILE);
            stringstream productStream;
            string line;

            // 读取整个文件内容
            while (getline(inFile, line)) {
                // 如果有过滤条件，只返回匹配的商品
                if (filter.empty()) {
                    productStream << line << "\n";
                } else {
                    // 检查商品名称是否包含过滤词
                    vector<string> productData = split(line, ',');
                    if (productData.size() >= 1 && productData[0].find(filter) != string::npos) {
                        productStream << line << "\n";
                    }
                }
            }
            inFile.close();

            response = "PRODUCTS " + productStream.str();
        }
        else if (command == "GET_CART") {
            // 获取购物车: GET_CART username
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            string username = currentUser->getUserName();
            string cartFile = username + "_cart.csv";
            ifstream inFile(cartFile);

            if (!inFile.good()) {
                response = "CART_EMPTY";
            } else {
                stringstream cartStream;
                string line;
                while (getline(inFile, line)) {
                    cartStream << line << "\n";
                }
                inFile.close();
                response = "CART " + cartStream.str();
            }
        }
        else if (command == "ADD_TO_CART") {
            // 添加到购物车: ADD_TO_CART productName quantity
            if (!currentUser) {
                response = "ERROR Not logged in";
            }
            else {
                // 获取完整的请求字符串
                string requestParams;
                getline(iss, requestParams);

                // 安全地处理可能为空的字符串
                if (requestParams.empty()) {
                    response = "ERROR Invalid request format";
                }
                else {
                    // 跳过前导空格
                    if (requestParams[0] == ' ') {
                        requestParams = requestParams.substr(1);
                    }

                    // 查找最后一个空格，分隔商品名称和数量
                    size_t lastSpacePos = requestParams.find_last_of(' ');
                    if (lastSpacePos == string::npos) {
                        response = "ERROR Invalid request format";
                    }
                    else {
                        string productName = requestParams.substr(0, lastSpacePos);
                        string quantityStr = requestParams.substr(lastSpacePos + 1);

                        // 检查数量字符串是否为空或无效
                        if (quantityStr.empty()) {
                            response = "ERROR Invalid quantity format";
                        }
                        else {
                            // 尝试将字符串转换为整数
                            int quantity;
                            try {
                                quantity = stoi(quantityStr);
                            } catch (const std::exception& e) {
                                response = "ERROR Invalid quantity format";
                                quantity = -1; // 设置无效值
                            }

                                // 查找商品
                                std::lock_guard<std::mutex> lock(fileMutex);
                                ifstream prodFile(PRODUCTS_FILE);
                                string line;
                                bool productFound = false;
                                string productPrice, productType, merchantName;
                                int availableQuantity = 0;

                                // 跳过CSV文件头
                                if (getline(prodFile, line)) {
                                    // 文件头不处理
                                }

                                while (getline(prodFile, line)) {
                                    vector<string> productData = split(line, ',');
                                    if (productData.size() >= 6 && productData[0] == productName) {
                                        productFound = true;
                                        productPrice = productData[2];
                                        availableQuantity = stoi(productData[3]);
                                        productType = productData[4];
                                        merchantName = productData[5];
                                        break;
                                    }
                                    else if (productData.size() == 5 && productData[0] == productName) {
                                        // 处理没有商家信息的旧格式数据
                                        productFound = true;
                                        productPrice = productData[2];
                                        availableQuantity = stoi(productData[3]);
                                        productType = productData[4];
                                        merchantName = "unknown"; // 设置默认商家名称
                                        break;
                                    }
                                }
                                prodFile.close();

                                if (!productFound) {
                                    response = "ERROR Product not found: '" + productName + "'";
                                }
                                else if (quantity <= 0 || quantity > availableQuantity) {
                                    response = "ERROR Invalid quantity. Available: " + to_string(availableQuantity);
                                }
                                else {

            // 添加到购物车文件
            string cartFile = currentUser->getUserName() + "_cart.csv";
            bool cartExists = ifstream(cartFile).good();
            vector<vector<string>> cartItems;

            if (cartExists) {
                ifstream inFile(cartFile);
                string line;

                // 读取CSV文件头
                if (getline(inFile, line)) {
                    cartItems.push_back(split(line, ','));
                }

                // 读取所有购物车项目
                while (getline(inFile, line)) {
                    cartItems.push_back(split(line, ','));
                }
                inFile.close();
            } else {
                cartItems.push_back({"name", "price", "quantity", "type", "merchant"});
            }

            // 检查购物车中是否已有该商品
            bool itemExists = false;
            bool quantityError = false;
            for (size_t i = 1; i < cartItems.size(); i++) {
                if (cartItems[i].size() >= 4 && cartItems[i][0] == productName) {
                    itemExists = true;
                    int newQuantity = stoi(cartItems[i][2]) + quantity;
                    if (newQuantity > availableQuantity) {
                        response = "ERROR Cannot add more. Available: " + to_string(availableQuantity);
                        quantityError = true;
                        break;
                    }
                    cartItems[i][2] = to_string(newQuantity);
                    break;
                }
            }

            // 如果没有数量错误，继续处理
            if (!quantityError) {
                // 如果购物车中没有该商品，则添加
                if (!itemExists) {
                    cartItems.push_back({productName, productPrice, to_string(quantity), productType, merchantName});
                }

                // 写回购物车文件
                ofstream outFile(cartFile);
                for (const auto& item : cartItems) {
                    outFile << join(item, ',') << endl;
                }
                outFile.close();

                                    response = "CART_UPDATED";
                                }
                            }
                        }
                    }
                }
            }
        }
        else if (command == "REMOVE_FROM_CART") {
            // 从购物车移除: REMOVE_FROM_CART index
            if (!currentUser) {
                response = "ERROR Not logged in";
            }
            else {

                int itemIndex;
                iss >> itemIndex;

                string cartFile = currentUser->getUserName() + "_cart.csv";
                ifstream inFile(cartFile);

                if (!inFile.good()) {
                    response = "ERROR Cart is empty";
                }
                else {
                    vector<vector<string>> cartItems;
                    string line;

                    // 读取CSV文件头
                    if (getline(inFile, line)) {
                        cartItems.push_back(split(line, ','));
                    }

                    // 读取所有购物车项目
                    while (getline(inFile, line)) {
                        cartItems.push_back(split(line, ','));
                    }
                    inFile.close();

                    if (itemIndex < 1 || itemIndex >= static_cast<int>(cartItems.size())) {
                        response = "ERROR Invalid item index";
                    }
                    else {
                        cartItems.erase(cartItems.begin() + itemIndex);

                        // 写回购物车文件
                        ofstream outFile(cartFile);
                        for (const auto& item : cartItems) {
                            outFile << join(item, ',') << endl;
                        }
                        outFile.close();

                        response = "CART_UPDATED";
                    }
                }
            }
        }
        else if (command == "UPDATE_CART_QUANTITY") {
            // 更新购物车商品数量: UPDATE_CART_QUANTITY index quantity
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            int itemIndex, newQuantity;
            iss >> itemIndex >> newQuantity;

            string cartFile = currentUser->getUserName() + "_cart.csv";
            ifstream inFile(cartFile);

            if (!inFile.good()) {
                response = "ERROR Cart is empty";
                continue;
            }

            vector<vector<string>> cartItems;
            string line;

            // 读取CSV文件头
            if (getline(inFile, line)) {
                cartItems.push_back(split(line, ','));
            }

            // 读取所有购物车项目
            while (getline(inFile, line)) {
                cartItems.push_back(split(line, ','));
            }
            inFile.close();

            if (itemIndex < 1 || itemIndex >= static_cast<int>(cartItems.size())) {
                response = "ERROR Invalid item index";
                continue;
            }

            // 获取商品名称和当前数量
            string productName = cartItems[itemIndex][0];

            // 查询商品可用库存
            ifstream prodFile(PRODUCTS_FILE);
            int availableQuantity = 0;

            // 跳过CSV文件头
            if (getline(prodFile, line)) {
                // 文件头不处理
            }

            while (getline(prodFile, line)) {
                vector<string> productData = split(line, ',');
                if (productData.size() >= 5 && productData[0] == productName) {
                    availableQuantity = stoi(productData[3]);
                    break;
                }
            }
            prodFile.close();

            if (newQuantity <= 0) {
                response = "ERROR Invalid quantity. Quantity must be positive.";
                continue;
            }

            if (newQuantity > availableQuantity) {
                response = "ERROR Not enough stock available. Available: " + to_string(availableQuantity);
                continue;
            }

            // 更新购物车中的数量
            cartItems[itemIndex][2] = to_string(newQuantity);

            // 写回购物车文件
            ofstream outFile(cartFile);
            for (const auto& item : cartItems) {
                outFile << join(item, ',') << endl;
            }
            outFile.close();

            response = "CART_UPDATED";
        }
        else if (command == "GENERATE_ORDER") {
            // 生成订单: GENERATE_ORDER selectedItems
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            string selectedItemsStr;
            getline(iss, selectedItemsStr);

            // 安全地处理可能为空的字符串
            if (selectedItemsStr.empty()) {
                response = "ERROR No items selected";
                continue;
            }

            // 跳过前导空格（确保字符串非空且第一个字符是空格）
            if (!selectedItemsStr.empty() && selectedItemsStr[0] == ' ') {
                selectedItemsStr = selectedItemsStr.substr(1);
            }

            // 检查选择的项目字符串是否为空
            if (selectedItemsStr.empty()) {
                response = "ERROR No items selected";
                continue;
            }

            vector<int> selectedItems;
            istringstream itemsStream(selectedItemsStr);
            string itemStr;

            // 处理可能的"all"选项
            if (selectedItemsStr == "all") {
                // 稍后会处理所有项目
            } else {
                // 解析数字项目
                while (itemsStream >> itemStr) {
                    try {
                        int item = stoi(itemStr);
                        selectedItems.push_back(item);
                    } catch (const std::exception& e) {
                        // 忽略无效的数字
                    }
                }
            }

            // 从文件中读取购物车数据
            string cartFile = currentUser->getUserName() + "_cart.csv";
            ifstream inFile(cartFile);

            if (!inFile.good()) {
                response = "ERROR Cart is empty";
                continue;
            }

            vector<vector<string>> cartItems;
            string line;

            // 读取CSV文件头
            if (getline(inFile, line)) {
                cartItems.push_back(split(line, ','));
            }

            // 读取所有购物车项目
            while (getline(inFile, line)) {
                cartItems.push_back(split(line, ','));
            }
            inFile.close();

            if (cartItems.size() <= 1) {
                response = "ERROR Cart is empty";
                continue;
            }

            // 创建选中项目的标记
            vector<bool> itemSelected(cartItems.size(), false);
            itemSelected[0] = true; // 标题行默认选中

            // 如果选择了"all"，则选择所有项目
            if (selectedItemsStr == "all") {
                for (size_t i = 1; i < cartItems.size(); i++) {
                    itemSelected[i] = true;
                }
            } else {
                // 否则根据选择的索引标记项目
                for (int idx : selectedItems) {
                    if (idx > 0 && idx < static_cast<int>(cartItems.size())) {
                        itemSelected[idx] = true;
                    }
                }
            }

            // 检查是否有选中的商品
            bool hasSelected = false;
            for (size_t i = 1; i < itemSelected.size(); i++) {
                if (itemSelected[i]) {
                    hasSelected = true;
                    break;
                }
            }

            if (!hasSelected) {
                response = "ERROR No items selected";
                continue;
            }

            // 检查库存是否足够
            bool stockSufficient = true;
            string stockError;

            for (size_t i = 1; i < cartItems.size(); i++) {
                if (itemSelected[i] && cartItems[i].size() >= 4) {
                    string productName = cartItems[i][0];
                    int quantity = stoi(cartItems[i][2]);

                    // 查询商品可用库存
                    ifstream prodFile(PRODUCTS_FILE);
                    string line;
                    int availableQuantity = 0;

                    // 跳过CSV文件头
                    if (getline(prodFile, line)) {
                        // 文件头不处理
                    }

                    while (getline(prodFile, line)) {
                        vector<string> productData = split(line, ',');
                        if (productData.size() >= 5 && productData[0] == productName) {
                            availableQuantity = stoi(productData[3]);
                            break;
                        }
                    }
                    prodFile.close();

                    if (quantity > availableQuantity) {
                        stockSufficient = false;
                        stockError = "Not enough stock for " + productName + ". Available: " + to_string(availableQuantity);
                        break;
                    }
                }
            }

            if (!stockSufficient) {
                response = "ERROR " + stockError;
                continue;
            }

            // 计算订单总金额
            double totalAmount = 0;
            vector<vector<string>> orderItems;
            orderItems.push_back({"name", "price", "quantity", "type", "merchant"});

            for (size_t i = 1; i < cartItems.size(); i++) {
                if (itemSelected[i] && cartItems[i].size() >= 4) {
                    string productName = cartItems[i][0];
                    double price = stod(cartItems[i][1]);
                    int quantity = stoi(cartItems[i][2]);
                    string type = cartItems[i][3];
                    string merchant = cartItems[i].size() >= 5 ? cartItems[i][4] : "unknown";

                    totalAmount += price * quantity;
                    vector<string> orderItem = {productName, to_string(price), to_string(quantity), type, merchant};
                    orderItems.push_back(orderItem);
                }
            }

            // 生成订单ID (时间戳 + 用户名)
            string orderId = to_string(time(nullptr)) + "_" + currentUser->getUserName();

            // 冻结商品库存
            for (size_t i = 1; i < orderItems.size(); i++) {
                string productName = orderItems[i][0];
                int quantity = stoi(orderItems[i][2]);

                // 读取所有产品
                vector<vector<string>> products;
                ifstream prodFile(PRODUCTS_FILE);
                string line;

                // 读取CSV文件头
                if (getline(prodFile, line)) {
                    products.push_back(split(line, ','));
                }

                // 读取所有产品数据
                while (getline(prodFile, line)) {
                    vector<string> productData = split(line, ',');
                    if (productData.size() >= 5 && productData[0] == productName) {
                        // 更新库存 (冻结)
                        int newQuantity = stoi(productData[3]) - quantity;
                        productData[3] = to_string(newQuantity);
                    }
                    products.push_back(productData);
                }
                prodFile.close();

                // 写回CSV文件
                ofstream outFile(PRODUCTS_FILE);
                for (const auto& product : products) {
                    outFile << join(product, ',') << endl;
                }
                outFile.close();
            }

            // 保存订单到文件
            string ordersFile = "orders.csv";
            bool ordersFileExists = ifstream(ordersFile).good();

            ofstream outOrdersFile(ordersFile, ios::app);
            if (!ordersFileExists) {
                outOrdersFile << "id,user,date,status,total" << endl;
            }

            // 获取当前时间
            time_t now = time(nullptr);
            char timeBuffer[80];
            strftime(timeBuffer, sizeof(timeBuffer), "%Y-%m-%d %H:%M:%S", localtime(&now));

            // 使用固定格式保存金额，确保使用点号作为小数分隔符
            outOrdersFile << orderId << "," << currentUser->getUserName() << ","
                         << timeBuffer << ",unpaid," << fixed << setprecision(2) << totalAmount << endl;
            outOrdersFile.close();

            // 将订单信息添加到对应商家的订单文件中
            // 按商家分组计算订单金额
            map<string, double> merchantOrderAmounts;
            for (size_t i = 1; i < orderItems.size(); i++) {
                if (orderItems[i].size() >= 5) {
                    string merchantName = orderItems[i][4];
                    double price = stod(orderItems[i][1]);
                    int quantity = stoi(orderItems[i][2]);
                    double itemTotal = price * quantity;

                    if (merchantOrderAmounts.find(merchantName) == merchantOrderAmounts.end()) {
                        merchantOrderAmounts[merchantName] = 0;
                    }
                    merchantOrderAmounts[merchantName] += itemTotal;
                }
            }

            // 为每个商家添加订单记录
            auto users = loadUsers();
            for (const auto& merchantOrder : merchantOrderAmounts) {
                string merchantName = merchantOrder.first;
                double merchantAmount = merchantOrder.second;

                // 查找商家用户
                for (auto& user : users) {
                    if (user->getUserName() == merchantName && user->getUserType() == "merchant") {
                        // 添加订单记录到商家的订单文件
                        auto merchant = dynamic_pointer_cast<Merchant>(user);
                        if (merchant) {
                            merchant->receiveOrder(orderId, currentUser->getUserName(), timeBuffer, merchantAmount);
                        }
                        break;
                    }
                }
            }

            // 不再创建单独的订单详情文件
            // 订单详情将存储在用户总订单文件中

            // 从购物车中移除已生成订单的商品
            vector<vector<string>> remainingItems;
            remainingItems.push_back(cartItems[0]); // 保留标题行

            for (size_t i = 1; i < cartItems.size(); i++) {
                if (!itemSelected[i]) {
                    remainingItems.push_back(cartItems[i]);
                }
            }

            // 更新购物车文件
            ofstream outCartFile(cartFile);
            for (const auto& item : remainingItems) {
                outCartFile << join(item, ',') << endl;
            }
            outCartFile.close();

            response = "ORDER_GENERATED " + orderId + " " + to_string(totalAmount);
        }
        else if (command == "GET_ORDERS") {
            // 获取订单: GET_ORDERS
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            string ordersFile = "orders.csv";
            ifstream inFile(ordersFile);

            if (!inFile.good()) {
                response = "NO_ORDERS";
                continue;
            }

            string line;
            stringstream ordersStream;

            // 读取CSV文件头
            if (getline(inFile, line)) {
                ordersStream << line << "\n";
            }

            // 读取所有订单
            bool hasOrders = false;
            while (getline(inFile, line)) {
                vector<string> orderData = split(line, ',');
                if (orderData.size() >= 5 && orderData[1] == currentUser->getUserName()) {
                    ordersStream << line << "\n";
                    hasOrders = true;
                }
            }
            inFile.close();

            if (!hasOrders) {
                response = "NO_ORDERS";
            } else {
                response = "ORDERS " + ordersStream.str();
            }
        }
        else if (command == "GET_ORDER_DETAILS") {
            // 获取订单详情: GET_ORDER_DETAILS orderId
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            string orderId;
            iss >> orderId;

            // 从总订单文件中查找订单信息
            string ordersFile = "orders.csv";
            ifstream ordersFileStream(ordersFile);

            if (!ordersFileStream.good()) {
                response = "ERROR Orders file not found";
                continue;
            }

            string line;
            bool orderFound = false;
            string orderUser, orderDate, orderStatus;
            double orderTotal = 0.0;

            // 跳过CSV文件头
            if (getline(ordersFileStream, line)) {
                // 文件头不处理
            }

            // 查找订单基本信息
            while (getline(ordersFileStream, line)) {
                vector<string> orderData = split(line, ',');
                if (orderData.size() >= 5 && orderData[0] == orderId) {
                    orderUser = orderData[1];
                    orderDate = orderData[2];
                    orderStatus = orderData[3];
                    string totalStr = orderData[4];
                    // 清理字符串中的空白字符
                    totalStr.erase(0, totalStr.find_first_not_of(" \t\r\n"));
                    totalStr.erase(totalStr.find_last_not_of(" \t\r\n") + 1);
                    orderTotal = stod(totalStr);
                    orderFound = true;
                    break;
                }
            }
            ordersFileStream.close();

            if (!orderFound) {
                response = "ERROR Order not found";
                continue;
            }

            // 构造订单详情响应（简化版本，只包含基本信息）
            stringstream detailsStream;
            detailsStream << "name,price,quantity,type,merchant\n";
            detailsStream << "Order Summary," << orderTotal << ",1,Order," << orderUser << "\n";

            response = "ORDER_DETAILS " + detailsStream.str();
        }
        else if (command == "PAY_ORDER") {
            // 支付订单: PAY_ORDER orderId password
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            string orderId, password;
            iss >> orderId >> password;

            // 验证密码
            if (!currentUser->verifyPassword(password)) {
                response = "ERROR Incorrect password";
                continue;
            }

            // 查找订单信息
            string ordersFile = "orders.csv";
            ifstream inFile(ordersFile);

            if (!inFile.good()) {
                response = "ERROR Order not found";
                continue;
            }

            string line;
            vector<vector<string>> orders;
            bool orderFound = false;
            int orderIndex = -1;

            // 读取CSV文件头
            if (getline(inFile, line)) {
                orders.push_back(split(line, ','));
            }

            // 读取所有订单
            while (getline(inFile, line)) {
                vector<string> orderData = split(line, ',');
                orders.push_back(orderData);
                if (orderData.size() >= 5 && orderData[0] == orderId) {
                    orderFound = true;
                    orderIndex = orders.size() - 1;
                }
            }
            inFile.close();

            if (!orderFound) {
                response = "ERROR Order not found";
                continue;
            }

            if (orders[orderIndex][3] != "unpaid") {
                response = "ERROR This order is already " + orders[orderIndex][3];
                continue;
            }

            if (orders[orderIndex].size() < 5) {
                response = "ERROR Order data incomplete";
                continue;
            }

            string totalAmountStr = orders[orderIndex][4];

            // 清理字符串中的空白字符
            totalAmountStr.erase(0, totalAmountStr.find_first_not_of(" \t\r\n"));
            totalAmountStr.erase(totalAmountStr.find_last_not_of(" \t\r\n") + 1);

            double totalAmount;
            try {
                totalAmount = stod(totalAmountStr);
            } catch (const std::exception& e) {
                response = "ERROR Invalid order amount: '" + totalAmountStr + "' - " + e.what();
                continue;
            }

            // 检查余额
            if (currentUser->queryBalance() < totalAmount) {
                response = "ERROR Insufficient balance. Current: " + to_string(currentUser->queryBalance()) + ", Required: " + to_string(totalAmount);
                continue;
            }

            // 扣除消费者余额
            if (!currentUser->consume(totalAmount)) {
                response = "ERROR Payment failed";
                continue;
            }

            // 支付成功后，需要将金额转入对应的商家账户并更新商家订单状态
            // 查找所有商家用户，检查他们的订单文件中是否有这个订单
            auto users = loadUsers();
            for (auto& user : users) {
                if (user->getUserType() == "merchant") {
                    auto merchant = dynamic_pointer_cast<Merchant>(user);
                    if (merchant) {
                        string merchantOrdersFile = user->getUserName() + "_orders.csv";
                        ifstream checkFile(merchantOrdersFile);

                        if (checkFile.good()) {
                            string line;
                            bool hasThisOrder = false;
                            double merchantAmount = 0.0;

                            // 跳过CSV文件头
                            if (getline(checkFile, line)) {
                                // 文件头不处理
                            }

                            // 查找是否有这个订单
                            while (getline(checkFile, line)) {
                                vector<string> orderData = split(line, ',');
                                if (orderData.size() >= 4 && orderData[0] == orderId) {
                                    hasThisOrder = true;
                                    merchantAmount = stod(orderData[3]);
                                    break;
                                }
                            }
                            checkFile.close();

                            if (hasThisOrder) {
                                // 增加商家余额
                                user->recharge(merchantAmount);

                                // 更新商家订单状态
                                merchant->updateOrderStatus(orderId, "paid");
                            }
                        }
                    }
                }
            }

            // 更新订单状态
            orders[orderIndex][3] = "paid";

            // 写回订单文件
            ofstream outFile(ordersFile);
            for (const auto& order : orders) {
                outFile << join(order, ',') << endl;
            }
            outFile.close();

            response = "PAYMENT_SUCCESS";
        }
        else if (command == "GET_ORDER_STATUS") {
            // 获取订单状态: GET_ORDER_STATUS orderId
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            string orderId;
            iss >> orderId;

            if (orderId.empty()) {
                response = "ERROR Missing order ID";
                continue;
            }

            // 查找订单状态
            string ordersFile = "orders.csv";
            ifstream inFile(ordersFile);

            if (!inFile.good()) {
                response = "ERROR Orders file not found";
                continue;
            }

            string line;
            bool orderFound = false;
            string orderStatus = "unknown";

            // 跳过CSV文件头
            if (getline(inFile, line)) {
                // 文件头不处理
            }

            // 查找订单
            while (getline(inFile, line)) {
                vector<string> orderData = split(line, ',');
                if (orderData.size() >= 4 && orderData[0] == orderId) {
                    orderStatus = orderData[3]; // 状态在第4列
                    orderFound = true;
                    break;
                }
            }
            inFile.close();

            if (orderFound) {
                response = "ORDER_STATUS " + orderStatus;
            } else {
                response = "ERROR Order not found";
            }
        }
        else if (command == "GET_BALANCE") {
            // 获取余额: GET_BALANCE
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            // 重新从文件加载用户信息以获取最新余额
            auto users = loadUsers();
            double latestBalance = currentUser->queryBalance(); // 默认值

            for (auto& user : users) {
                if (user->getUserName() == currentUser->getUserName() &&
                    user->getUserType() == currentUser->getUserType()) {
                    latestBalance = user->queryBalance();
                    // 同步更新当前用户对象的余额
                    currentUser = user;
                    break;
                }
            }

            response = "BALANCE " + to_string(latestBalance);
        }
        else if (command == "RECHARGE") {
            // 充值: RECHARGE amount
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            double amount;
            iss >> amount;

            if (currentUser->recharge(amount)) {
                response = "RECHARGE_SUCCESS " + to_string(currentUser->queryBalance());
            } else {
                response = "ERROR Invalid recharge amount";
            }
        }
        else if (command == "CHANGE_PASSWORD") {
            // 修改密码: CHANGE_PASSWORD oldPassword newPassword
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            string oldPassword, newPassword;
            iss >> oldPassword >> newPassword;

            if (!currentUser->verifyPassword(oldPassword)) {
                response = "ERROR Incorrect old password";
                continue;
            }

            if (!isPasswordComplex(newPassword)) {
                response = "ERROR Password not complex enough";
                continue;
            }

            currentUser->changePassword(newPassword);
            response = "PASSWORD_CHANGED";
        }
        else if (command == "GET_MERCHANT_PRODUCTS") {
            // 获取商家自己的商品: GET_MERCHANT_PRODUCTS
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            if (currentUser->getUserType() != "merchant") {
                response = "ERROR Not a merchant";
                continue;
            }

            string merchantName = currentUser->getUserName();

            // 捕获输出
            stringstream productStream;
            productStream << "name,description,price,quantity,type,merchant" << endl;

            // 查找商家的商品
            std::lock_guard<std::mutex> lock(fileMutex);
            ifstream prodFile(PRODUCTS_FILE);
            string line;

            // 跳过CSV文件头
            if (getline(prodFile, line)) {
                // 文件头不处理
            }

            bool hasProducts = false;
            while (getline(prodFile, line)) {
                vector<string> productData = split(line, ',');
                if (productData.size() >= 6 && productData[5] == merchantName) {
                    productStream << line << endl;
                    hasProducts = true;
                }
            }
            prodFile.close();

            response = "PRODUCTS " + productStream.str();
        }
        else if (command == "ADD_PRODUCT") {
            // 添加商品: ADD_PRODUCT "name" "description" price quantity type
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            if (currentUser->getUserType() != "merchant") {
                response = "ERROR Not a merchant";
                continue;
            }

            // 获取完整的参数字符串
            string paramStr;
            getline(iss, paramStr);

            // 跳过前导空格
            if (!paramStr.empty() && paramStr[0] == ' ') {
                paramStr = paramStr.substr(1);
            }

            // 解析带引号的参数
            vector<string> params = parseQuotedParams(paramStr);

            // 验证参数数量
            if (params.size() != 5) {
                response = "ERROR Invalid parameter count. Expected: name description price quantity type";
                continue;
            }

            string name = params[0];
            string description = params[1];
            string priceStr = params[2];
            string quantityStr = params[3];
            string type = params[4];

            // 验证参数
            if (name.empty() || description.empty() || priceStr.empty() || quantityStr.empty() || type.empty()) {
                response = "ERROR Missing parameters";
                continue;
            }

            // 转换价格和数量
            double price;
            int quantity;
            try {
                price = stod(priceStr);
                quantity = stoi(quantityStr);
            } catch (const std::exception& e) {
                response = "ERROR Invalid price or quantity format";
                continue;
            }

            if (price <= 0 || quantity < 0) {
                response = "ERROR Price must be positive and quantity must be non-negative";
                continue;
            }

            // 添加商品
            auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
            merchant->addProduct(name, description, price, quantity, type);
            response = "PRODUCT_ADDED";
        }
        else if (command == "UPDATE_PRODUCT_PRICE") {
            // 更新商品价格: UPDATE_PRODUCT_PRICE "name" price
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            if (currentUser->getUserType() != "merchant") {
                response = "ERROR Not a merchant";
                continue;
            }

            // 获取完整的参数字符串
            string paramStr;
            getline(iss, paramStr);

            // 跳过前导空格
            if (!paramStr.empty() && paramStr[0] == ' ') {
                paramStr = paramStr.substr(1);
            }

            // 解析带引号的参数
            vector<string> params = parseQuotedParams(paramStr);

            // 验证参数数量
            if (params.size() != 2) {
                response = "ERROR Invalid parameter count. Expected: name price";
                continue;
            }

            string name = params[0];
            string priceStr = params[1];

            // 验证参数
            if (name.empty() || priceStr.empty()) {
                response = "ERROR Missing parameters";
                continue;
            }

            // 转换价格
            double price;
            try {
                price = stod(priceStr);
            } catch (const std::exception& e) {
                response = "ERROR Invalid price format";
                continue;
            }

            if (price <= 0) {
                response = "ERROR Price must be positive";
                continue;
            }

            // 更新商品价格 - manageProduct函数内部会处理锁和权限检查
            auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
            if (merchant->manageProduct(name, &price, nullptr)) {
                response = "PRODUCT_UPDATED";
            } else {
                response = "ERROR Product not found or not owned by you";
            }
        }
        else if (command == "UPDATE_PRODUCT_QUANTITY") {
            // 更新商品库存: UPDATE_PRODUCT_QUANTITY "name" quantity
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            if (currentUser->getUserType() != "merchant") {
                response = "ERROR Not a merchant";
                continue;
            }

            // 获取完整的参数字符串
            string paramStr;
            getline(iss, paramStr);

            // 跳过前导空格
            if (!paramStr.empty() && paramStr[0] == ' ') {
                paramStr = paramStr.substr(1);
            }

            // 解析带引号的参数
            vector<string> params = parseQuotedParams(paramStr);

            // 验证参数数量
            if (params.size() != 2) {
                response = "ERROR Invalid parameter count. Expected: name quantity";
                continue;
            }

            string name = params[0];
            string quantityStr = params[1];

            // 验证参数
            if (name.empty() || quantityStr.empty()) {
                response = "ERROR Missing parameters";
                continue;
            }

            // 转换数量
            int quantity;
            try {
                quantity = stoi(quantityStr);
            } catch (const std::exception& e) {
                response = "ERROR Invalid quantity format";
                continue;
            }

            if (quantity < 0) {
                response = "ERROR Quantity must be non-negative";
                continue;
            }

            // 更新商品库存 - manageProduct函数内部会处理锁和权限检查
            auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
            if (merchant->manageProduct(name, nullptr, &quantity)) {
                response = "PRODUCT_UPDATED";
            } else {
                response = "ERROR Product not found or not owned by you";
            }
        }

        else if (command == "GET_MERCHANT_ORDERS") {
            // 获取商家订单: GET_MERCHANT_ORDERS
            if (!currentUser) {
                response = "ERROR Not logged in";
                continue;
            }

            if (currentUser->getUserType() != "merchant") {
                response = "ERROR Not a merchant";
                continue;
            }

            string merchantName = currentUser->getUserName();
            stringstream ordersStream;
            ordersStream << "orderId,consumer,date,amount,status" << "\n";

            // 查找所有包含该商家商品的订单
            string ordersFile = "orders.csv";
            ifstream ordersInFile(ordersFile);

            if (!ordersInFile.good()) {
                response = "NO_ORDERS";
                continue;
            }

            string line;
            vector<vector<string>> allOrders;

            // 跳过CSV文件头
            if (getline(ordersInFile, line)) {
                // 文件头不处理
            }

            // 读取所有订单
            while (getline(ordersInFile, line)) {
                vector<string> orderData = split(line, ',');
                if (orderData.size() >= 5) {
                    allOrders.push_back(orderData);
                }
            }
            ordersInFile.close();

            bool hasOrders = false;

            // 简化版本：直接从商家的订单文件中读取
            string merchantOrdersFile = merchantName + "_orders.csv";
            ifstream merchantFile(merchantOrdersFile);

            if (merchantFile.good()) {
                string line;
                // 跳过CSV文件头
                if (getline(merchantFile, line)) {
                    // 文件头不处理
                }

                // 读取商家的订单记录
                while (getline(merchantFile, line)) {
                    ordersStream << line << "\n";
                    hasOrders = true;
                }
                merchantFile.close();
            }

            if (!hasOrders) {
                response = "NO_ORDERS";
            } else {
                response = "ORDERS " + ordersStream.str();
            }
        }
        else if (command == "LOGOUT") {
            // 登出: LOGOUT
            currentUser = nullptr;
            response = "LOGOUT_SUCCESS";
        }
        else if (command == "DISCONNECT") {
            // 断开连接: DISCONNECT
            response = "DISCONNECT_ACK";
            clientConnected = false;
        }
        else {
            response = "ERROR Unknown command";
        }

        // 输出服务器响应（但不显示敏感信息）
        string logResponse = response;
        if (logResponse.find("LOGIN_SUCCESS") == 0) {
            // 登录成功响应不需要隐藏
            cout << "Sending response: " << logResponse << endl;
        } else if (logResponse.length() > 500) {
            // 对于长响应，只显示前100个字符
            cout << "Sending response: " << logResponse.substr(0, 100) << "... [response truncated]" << endl;
        } else {
            cout << "Sending response: " << logResponse << endl;
        }

        // 发送响应
        send(clientSocket, response.c_str(), response.length(), 0);
    }

    // 关闭客户端套接字
    closesocket(clientSocket);
    cout << "Client disconnected." << endl;
}

// 主函数
int main() {
    // 初始化Winsock
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        cerr << "WSAStartup failed: " << result << endl;
        return 1;
    }

    // 创建套接字
    SOCKET serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (serverSocket == INVALID_SOCKET) {
        cerr << "Socket creation failed: " << WSAGetLastError() << endl;
        WSACleanup();
        return 1;
    }

    // 绑定套接字
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(PORT);

    result = bind(serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr));
    if (result == SOCKET_ERROR) {
        cerr << "Bind failed: " << WSAGetLastError() << endl;
        closesocket(serverSocket);
        WSACleanup();
        return 1;
    }

    // 监听连接
    result = listen(serverSocket, SOMAXCONN);
    if (result == SOCKET_ERROR) {
        cerr << "Listen failed: " << WSAGetLastError() << endl;
        closesocket(serverSocket);
        WSACleanup();
        return 1;
    }

    cout << "Server started. Listening on port " << PORT << "..." << endl;

    // 初始化商品数据
    initProducts();

    // 接受客户端连接
    vector<std::thread> clientThreads;

    while (serverRunning) {
        SOCKET clientSocket = accept(serverSocket, NULL, NULL);
        if (clientSocket == INVALID_SOCKET) {
            cerr << "Accept failed: " << WSAGetLastError() << endl;
            continue;
        }

        cout << "Client connected." << endl;
        clientThreads.push_back(std::thread(handleClient, clientSocket));
    }

    // 等待所有客户端线程结束
    for (auto& t : clientThreads) {
        if (t.joinable()) {
            t.join();
        }
    }

    // 关闭服务器套接字
    closesocket(serverSocket);
    WSACleanup();
    return 0;
}
