#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <memory>
#include <limits>
#include <map>
#include <ctime>  // 添加时间相关头文件，用于time(), localtime(), strftime()
#include <iomanip>
using namespace std;

// 文件路径常量
const string USERS_FILE = "users.csv";
const string PRODUCTS_FILE = "products.csv";

// 用户基类（抽象类）
class User {
public:
    User(const string& username, const string& password, double balance)
        : username(username), password(password), balance(balance) {}

    virtual string getUserType() const = 0;

    void changePassword(const string& newPassword) {
        password = newPassword;
        saveUserInfo();
    }

    double queryBalance() const {
        return balance;
    }

    bool recharge(double amount) {
        if (amount > 0) {
            balance += amount;
            saveUserInfo();
            return true;
        }
        return false;
    }

    bool consume(double amount) {
        if (amount > 0 && amount <= balance) {
            balance -= amount;
            saveUserInfo();
            return true;
        }
        return false;
    }

    string getUserName() const {
        return username;
    }

    bool verifyPassword(const string& pwd) const {
        return password == pwd;
    }

protected:
    string username;
    string password;
    double balance;

    virtual void saveUserInfo() = 0;
};

// 消费者类
class Consumer : public User {
public:
    Consumer(const string& username, const string& password, double balance)
        : User(username, password, balance) {}

    string getUserType() const override {
        return "consumer";
    }

protected:
    void saveUserInfo() override {
        vector<vector<string>> users;
        ifstream inFile(USERS_FILE);
        string line;

        // 读取CSV文件头
        if (getline(inFile, line)) {
            users.push_back(split(line, ','));
        }

        // 读取所有用户数据
        while (getline(inFile, line)) {
            vector<string> userData = split(line, ',');
            if (userData.size() >= 4 && userData[0] == username) {
                // 更新当前用户数据
                userData[1] = password;
                userData[2] = to_string(balance);
            }
            users.push_back(userData);
        }
        inFile.close();

        // 写回CSV文件
        ofstream outFile(USERS_FILE);
        for (const auto& user : users) {
            outFile << join(user, ',') << endl;
        }
        outFile.close();
    }

private:
    // 辅助函数：分割字符串
    vector<string> split(const string& s, char delimiter) {
        vector<string> tokens;
        string token;
        istringstream tokenStream(s);
        while (getline(tokenStream, token, delimiter)) {
            tokens.push_back(token);
        }
        return tokens;
    }

    // 辅助函数：连接字符串
    string join(const vector<string>& v, char delimiter) {
        string result;
        for (size_t i = 0; i < v.size(); ++i) {
            result += v[i];
            if (i < v.size() - 1) {
                result += delimiter;
            }
        }
        return result;
    }
};

// 商家类
class Merchant : public User {
public:
    Merchant(const string& username, const string& password, double balance)
        : User(username, password, balance) {}

    string getUserType() const override {
        return "merchant";
    }

    void addProduct(const string& name, const string& description, double price, int quantity, const string& type) {
        ofstream outFile(PRODUCTS_FILE, ios::app);
        outFile << name << "," << description << "," << price << "," << quantity << "," << type << "," << username << endl;
        outFile.close();
    }

    bool manageProduct(const string& name, double* price, int* quantity) {
        vector<vector<string>> products;
        ifstream inFile(PRODUCTS_FILE);
        string line;
        bool found = false;

        // 读取CSV文件头
        if (getline(inFile, line)) {
            products.push_back(split(line, ','));
        }

        // 读取所有产品数据
        while (getline(inFile, line)) {
            vector<string> productData = split(line, ',');
            if (productData.size() >= 5 && productData[0] == name) {
                // 更新产品数据
                if (price != nullptr) {
                    productData[2] = to_string(*price);
                }
                if (quantity != nullptr) {
                    productData[3] = to_string(*quantity);
                }
                found = true;
            }
            products.push_back(productData);
        }
        inFile.close();

        if (found) {
            // 写回CSV文件
            ofstream outFile(PRODUCTS_FILE);
            for (const auto& product : products) {
                outFile << join(product, ',') << endl;
            }
            outFile.close();
        }
        return found;
    }

    // 添加新方法：接收订单
    void receiveOrder(const string& orderId, const string& consumer, const string& date, double amount) {
        string merchantOrdersFile = username + "_orders.csv";
        bool fileExists = ifstream(merchantOrdersFile).good();

        ofstream outFile(merchantOrdersFile, ios::app);
        if (!fileExists) {
            outFile << "orderId,consumer,date,amount,status" << endl;
        }

        outFile << orderId << "," << consumer << "," << date << "," << amount << ",unpaid" << endl;
        outFile.close();
    }

    // 新增方法：更新订单状态
    void updateOrderStatus(const string& orderId, const string& newStatus) {
        string merchantOrdersFile = username + "_orders.csv";

        vector<vector<string>> orders;
        ifstream inFile(merchantOrdersFile);
        string line;

        if (!inFile.good()) {
            return; // 文件不存在，无需更新
        }

        // 读取CSV文件头
        if (getline(inFile, line)) {
            orders.push_back(split(line, ','));
        }

        // 读取所有订单数据
        while (getline(inFile, line)) {
            vector<string> orderData = split(line, ',');
            if (orderData.size() >= 5 && orderData[0] == orderId) {
                // 更新订单状态
                orderData[4] = newStatus;
            }
            orders.push_back(orderData);
        }
        inFile.close();

        // 写回CSV文件
        ofstream outFile(merchantOrdersFile);
        for (const auto& order : orders) {
            outFile << join(order, ',') << endl;
        }
        outFile.close();
    }

protected:
    void saveUserInfo() override {
        vector<vector<string>> users;
        ifstream inFile(USERS_FILE);
        string line;

        // 读取CSV文件头
        if (getline(inFile, line)) {
            users.push_back(split(line, ','));
        }

        // 读取所有用户数据
        while (getline(inFile, line)) {
            vector<string> userData = split(line, ',');
            if (userData.size() >= 4 && userData[0] == username) {
                // 更新当前用户数据
                userData[1] = password;
                userData[2] = to_string(balance);
            }
            users.push_back(userData);
        }
        inFile.close();

        // 写回CSV文件
        ofstream outFile(USERS_FILE);
        for (const auto& user : users) {
            outFile << join(user, ',') << endl;
        }
        outFile.close();
    }

private:
    // 辅助函数：分割字符串
    vector<string> split(const string& s, char delimiter) {
        vector<string> tokens;
        string token;
        istringstream tokenStream(s);
        while (getline(tokenStream, token, delimiter)) {
            tokens.push_back(token);
        }
        return tokens;
    }

    // 辅助函数：连接字符串
    string join(const vector<string>& v, char delimiter) {
        string result;
        for (size_t i = 0; i < v.size(); ++i) {
            result += v[i];
            if (i < v.size() - 1) {
                result += delimiter;
            }
        }
        return result;
    }
};

// 商品基类
class Product {
public:
    Product(const string& name, const string& description, double price, int quantity)
        : name(name), description(description), price(price), quantity(quantity) {}

    virtual double getPrice() const {
        return price;
    }

    string getName() const {
        return name;
    }

    string getDescription() const {
        return description;
    }

    int getQuantity() const {
        return quantity;
    }

    void setPrice(double newPrice) {
        price = newPrice;
    }

    void setQuantity(int newQuantity) {
        quantity = newQuantity;
    }

protected:
    string name;
    string description;
    double price;
    int quantity;
};

// 图书类
class Book : public Product {
public:
    Book(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 食品类
class Food : public Product {
public:
    Food(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 日用品类
class DailyNecessities : public Product {
public:
    DailyNecessities(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 个人护理类
class PersonalCare : public Product {
public:
    PersonalCare(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 服装类
class Clothing : public Product {
public:
    Clothing(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 电器类
class Appliances : public Product {
public:
    Appliances(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 文具和体育用品类
class StationeryAndSportsGoods : public Product {
public:
    StationeryAndSportsGoods(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 玩具类
class Toys : public Product {
public:
    Toys(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 饮料类
class Beverages : public Product {
public:
    Beverages(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 母婴用品类
class MaternityAndBabyProducts : public Product {
public:
    MaternityAndBabyProducts(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 其他类
class Others : public Product {
public:
    Others(const string& name, const string& description, double price, int quantity)
        : Product(name, description, price, quantity) {}
};

// 辅助函数：分割字符串
vector<string> split(const string& s, char delimiter) {
    vector<string> tokens;
    string token;
    istringstream tokenStream(s);
    while (getline(tokenStream, token, delimiter)) {
        tokens.push_back(token);
    }
    return tokens;
}

// 辅助函数：连接字符串
string join(const vector<string>& v, char delimiter) {
    string result;
    for (size_t i = 0; i < v.size(); ++i) {
        result += v[i];
        if (i < v.size() - 1) {
            result += delimiter;
        }
    }
    return result;
}

// 加载用户信息
vector<shared_ptr<User>> loadUsers() {
    vector<shared_ptr<User>> users;
    ifstream inFile(USERS_FILE);
    string line;

    // 跳过CSV文件头
    if (getline(inFile, line)) {
        // 文件头不处理
    }

    while (getline(inFile, line)) {
        vector<string> userData = split(line, ',');
        if (userData.size() >= 4) {
            string username = userData[0];
            string password = userData[1];
            double balance = stod(userData[2]);
            string type = userData[3];

            if (type == "consumer") {
                users.emplace_back(make_shared<Consumer>(username, password, balance));
            } else if (type == "merchant") {
                users.emplace_back(make_shared<Merchant>(username, password, balance));
            }
        }
    }
    inFile.close();
    return users;
}

// 检查密码复杂度
bool isPasswordComplex(const string& password) {
    if (password.length() < 6) {
        return false;
    }

    bool hasLetter = false;
    bool hasDigit = false;
    bool hasSymbol = false;

    for (char c : password) {
        if (isalpha(c)) {
            hasLetter = true;
        } else if (isdigit(c)) {
            hasDigit = true;
        } else {
            hasSymbol = true;
        }
    }

    return hasLetter && hasDigit && hasSymbol;
}

// 检查用户名是否已存在
bool isUsernameExists(const string& username) {
    auto users = loadUsers();
    for (const auto& user : users) {
        if (user->getUserName() == username) {
            return true;
        }
    }
    return false;
}

// 用户注册
shared_ptr<User> registerUser(const string& username, const string& password, const string& type) {
    // 检查用户名是否已存在
    if (isUsernameExists(username)) {
        cout << "This username already exists. Please choose another one." << endl;
        return nullptr;
    }

    // 检查密码复杂度
    if (!isPasswordComplex(password)) {
        cout << "Password must be at least 6 characters long and contain letters, digits, and symbols." << endl;
        return nullptr;
    }

    shared_ptr<User> newUser;
    if (type == "consumer") {
        newUser = make_shared<Consumer>(username, password, 0);
    } else if (type == "merchant") {
        newUser = make_shared<Merchant>(username, password, 0);
    } else {
        cout << "Invalid user type. Please enter 'consumer' or 'merchant'." << endl;
        return nullptr;
    }

    // 检查文件是否存在，如果不存在则创建并添加表头
    ifstream checkFile(USERS_FILE);
    if (!checkFile.good()) {
        ofstream createFile(USERS_FILE);
        createFile << "username,password,balance,type" << endl;
        createFile.close();
    } else {
        checkFile.close();
    }

    ofstream outFile(USERS_FILE, ios::app);
    outFile << username << "," << password << "," << 0 << "," << type << endl;
    outFile.close();

    cout << "Registration successful!" << endl;
    return newUser;
}

// 用户登录
shared_ptr<User> login(const string& username, const string& password) {
    auto users = loadUsers();

    for (const auto& user : users) {
        if (user->getUserName() == username) {
            if (user->verifyPassword(password)) {
                return user;
            } else {
                return nullptr;
            }
        }
    }

    return nullptr;
}

// 展示平台商品信息
void showProducts(const string& filter = "") {
    ifstream inFile(PRODUCTS_FILE);
    string line;

    // 跳过CSV文件头
    if (getline(inFile, line)) {
        // 文件头不处理
    }

    while (getline(inFile, line)) {
        vector<string> productData = split(line, ',');
        if (productData.size() >= 6) {  // 确保至少有6个字段
            string name = productData[0];
            string description = productData[1];
            double price = stod(productData[2]);
            int quantity = stoi(productData[3]);
            string type = productData[4];
            string merchant = productData[5];

            if (filter.empty() || name.find(filter) != string::npos) {
                cout << "Name: " << name << ", Description: " << description
                     << ", Price: " << price << ", Quantity: " << quantity
                     << ", Type: " << type << ", Merchant: " << merchant << endl;
            }
        }
    }
    inFile.close();
}

// 同一品类商品打折
void discountProducts(const string& type, double discountRate) {
    vector<vector<string>> products;
    ifstream inFile(PRODUCTS_FILE);
    string line;

    // 读取CSV文件头
    if (getline(inFile, line)) {
        products.push_back(split(line, ','));
    }

    // 读取所有产品数据
    while (getline(inFile, line)) {
        vector<string> productData = split(line, ',');
        if (productData.size() >= 5 && productData[4] == type) {
            // 应用折扣
            double price = stod(productData[2]);
            price *= discountRate;
            productData[2] = to_string(price);
        }
        products.push_back(productData);
    }
    inFile.close();

    // 写回CSV文件
    ofstream outFile(PRODUCTS_FILE);
    for (const auto& product : products) {
        outFile << join(product, ',') << endl;
    }
    outFile.close();
}

// 初始化商品数据
void initProducts() {
    ifstream checkFile(PRODUCTS_FILE);
    if (!checkFile.good()) {
        ofstream outFile(PRODUCTS_FILE);
        outFile << "name,description,price,quantity,type,merchant" << endl;
        outFile << "C++ Primer,A classic C++ learning book,100,20,Book,huan" << endl;
        outFile << "Effective C++,A book to improve C++ programming skills,80,15,Book,huan" << endl;
        outFile << "The C++ Programming Language,The authoritative C++ language book,120,10,Book,huan" << endl;
        outFile << "T-Shirt,A simple white T-shirt,50,30,Clothing,huan" << endl;
        outFile << "Jeans,Blue jeans,150,25,Clothing,huan" << endl;
        outFile << "Sweater,A gray sweater,120,20,Clothing,huan" << endl;
        outFile << "Chocolate,Rich chocolate,20,50,Food,huan" << endl;
        outFile << "Biscuit,Delicious biscuits,10,60,Food,huan" << endl;
        outFile << "Candy,Fruit hard candies,5,80,Food,huan" << endl;
        outFile << "Toothpaste,Mint flavor toothpaste,15,40,Personal Care" << endl;
        outFile << "Shampoo,Moisturizing shampoo,25,35,Personal Care" << endl;
        outFile << "Soap,Antibacterial soap,8,70,Personal Care" << endl;
        outFile << "Toilet Paper,Soft toilet paper,12,100,Daily Necessities" << endl;
        outFile << "Detergent,Powerful cleaning detergent,30,45,Daily Necessities" << endl;
        outFile << "Refrigerator,Energy-efficient refrigerator,2000,5,Appliances" << endl;
        outFile << "Microwave,Compact microwave oven,500,8,Appliances" << endl;
        outFile << "Notebook,Lined notebook,5,200,Stationery and Sports Goods" << endl;
        outFile << "Basketball,Standard size basketball,60,15,Stationery and Sports Goods" << endl;
        outFile << "Teddy Bear,Soft plush teddy bear,40,25,Toys" << endl;
        outFile << "Building Blocks,Educational building blocks,35,30,Toys" << endl;
        outFile << "Cola,Refreshing cola drink,3,150,Beverages" << endl;
        outFile << "Orange Juice,Fresh orange juice,4,120,Beverages" << endl;
        outFile << "Baby Diapers,Absorbent baby diapers,50,40,Maternity and Baby Products" << endl;
        outFile << "Baby Formula,Nutritious baby formula,80,30,Maternity and Baby Products" << endl;
        outFile << "Gift Card,Redeemable gift card,100,50,Others" << endl;
        outFile.close();
    }
    checkFile.close();
}

// 前向声明
void showUserMenu(shared_ptr<User> currentUser);
void showMainMenu();
void registerScreen();
void loginScreen();
void displayProductsScreen(shared_ptr<User> currentUser = nullptr);
void searchProductsScreen(shared_ptr<User> currentUser = nullptr);
void changePasswordScreen(shared_ptr<User> currentUser);
void checkBalanceScreen(shared_ptr<User> currentUser);
void rechargeScreen(shared_ptr<User> currentUser);
void makePurchaseScreen(shared_ptr<User> currentUser);
void addProductScreen(shared_ptr<User> currentUser);
void manageProductsScreen(shared_ptr<User> currentUser);
void applyDiscountScreen(shared_ptr<User> currentUser);
void shoppingCartScreen(shared_ptr<User> currentUser);
void generateOrderScreen(shared_ptr<User> currentUser);
void viewOrdersScreen(shared_ptr<User> currentUser);
void payOrderScreen(shared_ptr<User> currentUser, const string& orderId);
void viewMerchantOrdersScreen(shared_ptr<User> currentUser);
void viewMerchantOrderDetailsScreen(shared_ptr<User> currentUser, const string& orderId);

// 显示主菜单并处理选择
void showMainMenu() {
    int choice = 0;

    system("cls");  // 清屏，Windows系统使用
    // system("clear");  // 清屏，Linux/Mac系统使用
    cout << "\n===== E-Commerce Platform =====\n";
    cout << "1. Register\n";
    cout << "2. Login\n";
    cout << "3. Display Products\n";
    cout << "4. Search Products\n";
    cout << "5. Exit\n";
    cout << "Please enter your choice: ";

    // 获取用户输入
    cin >> choice;
    if (cin.fail()) {
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        showMainMenu();  // 重新显示菜单
        return;
    }
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    // 处理用户选择
    switch (choice) {
        case 1:
            registerScreen();
            break;
        case 2:
            loginScreen();
            break;
        case 3:
            displayProductsScreen();
            break;
        case 4:
            searchProductsScreen();
            break;
        case 5:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Please try again." << endl;
            cout << "\nPress Enter to continue...";
            cin.get();
            showMainMenu();
    }
}

// 注册界面
void registerScreen() {
    system("cls");
    cout << "\n===== Register =====\n";

    string username, password, type;

    // 获取用户名并检查是否已存在
    while (true) {
        cout << "Please enter your username: ";
        getline(cin, username);

        if (isUsernameExists(username)) {
            cout << "This username already exists. Would you like to:" << endl;
            cout << "1. Try another username" << endl;
            cout << "2. Go to login page" << endl;
            cout << "3. Return to main menu" << endl;
            cout << "Please choose: ";

            int choice;
            cin >> choice;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            switch (choice) {
                case 1:
                    // 继续循环，重新输入用户名
                    break;
                case 2:
                    // 跳转到登录界面
                    loginScreen();
                    return;
                case 3:
                    // 返回主界面
                    showMainMenu();
                    return;
                default:
                    cout << "Invalid choice, defaulting to try another username." << endl;
                    break;
            }
        } else {
            break;
        }
    }

    // 获取密码并检查复杂度
    while (true) {
        cout << "Please enter your password (at least 6 characters, must include letters, digits, and symbols): ";
        getline(cin, password);

        if (!isPasswordComplex(password)) {
            cout << "Password is not complex enough. Please try again." << endl;
        } else {
            break;
        }
    }

    // 获取用户类型
    while (true) {
        cout << "Please enter your user type (consumer/merchant): ";
        getline(cin, type);

        if (type != "consumer" && type != "merchant") {
            cout << "Invalid user type. Please enter 'consumer' or 'merchant'." << endl;
        } else {
            break;
        }
    }

    shared_ptr<User> currentUser = registerUser(username, password, type);
    if (currentUser) {
        cout << "Press Enter to continue...";
        cin.get();
        showUserMenu(currentUser);
    } else {
        cout << "Registration failed. Press Enter to return to main menu...";
        cin.get();
        showMainMenu();
    }
}

// 登录界面
void loginScreen() {
    system("cls");
    cout << "\n===== Login =====\n";

    string username, password;
    shared_ptr<User> currentUser = nullptr;
    bool usernameValid = false;

    // 获取并验证用户名
    while (!usernameValid) {
        cout << "Please enter your username: ";
        getline(cin, username);

        // 检查用户名是否存在
        auto users = loadUsers();
        for (const auto& user : users) {
            if (user->getUserName() == username) {
                usernameValid = true;
                break;
            }
        }

        if (!usernameValid) {
            cout << "Username not found. Would you like to:" << endl;
            cout << "1. Try another username" << endl;
            cout << "2. Go to registration page" << endl;
            cout << "Please choose: ";

            int choice;
            cin >> choice;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            switch (choice) {
                case 1:
                    // 继续循环，重新输入用户名
                    break;
                case 2:
                    // 跳转到注册界面
                    registerScreen();
                    return;
                default:
                    cout << "Invalid choice, defaulting to try another username." << endl;
                    break;
            }
        }
    }

    // 获取并验证密码
    bool passwordValid = false;
    while (!passwordValid) {
        cout << "Please enter your password: ";
        getline(cin, password);

        currentUser = login(username, password);
        if (currentUser) {
            passwordValid = true;
        } else {
            cout << "Incorrect password. Please try again." << endl;
        }
    }

    showUserMenu(currentUser);
}

// 显示商品界面
void displayProductsScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Products =====\n";
    cout << "1. View all products\n";

    // 明确检查currentUser是否为空
    if (currentUser) {
        cout << "2. Return to user menu\n";
    } else {
        cout << "2. Return to main menu\n";
    }

    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续显示商品流程
        case 2:
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to previous menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
            return;
    }

    // 显示所有商品
    cout << "\nAll available products:\n";
    showProducts();

    cout << "\n1. Search for specific products\n";

    // 同样明确检查currentUser
    if (currentUser) {
        cout << "2. Return to user menu\n";
    } else {
        cout << "2. Return to main menu\n";
    }

    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            searchProductsScreen(currentUser);
            break;
        case 2:
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to previous menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
    }
}

// 搜索商品界面
void searchProductsScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Search Products =====\n";

    cout << "Please enter the search keyword (or type 'back' to return): ";
    string keyword;
    getline(cin, keyword);

    if (keyword == "back") {
        if (currentUser) {
            showUserMenu(currentUser);
        } else {
            showMainMenu();
        }
        return;
    }

    cout << "\nSearch results for '" << keyword << "':\n";
    showProducts(keyword);

    cout << "\n1. Search again\n";
    cout << "2. Return to " << (currentUser ? "user menu" : "main menu") << "\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            searchProductsScreen(currentUser);
            break;
        case 2:
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to previous menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            if (currentUser) {
                showUserMenu(currentUser);
            } else {
                showMainMenu();
            }
    }
}

// 显示用户菜单并处理选择
void showUserMenu(shared_ptr<User> currentUser) {
    int choice = 0;

    system("cls");  // 清屏
    cout << "\n===== User Menu =====\n";
    cout << "Welcome, " << currentUser->getUserName() << " (" << currentUser->getUserType() << ")!\n\n";

    if (currentUser->getUserType() == "consumer") {
        cout << "1. Change Password\n";
        cout << "2. Check Balance\n";
        cout << "3. Recharge\n";
        cout << "4. Make Purchase\n";
        cout << "5. Shopping Cart\n";
        cout << "6. Generate Order\n";
        cout << "7. View Orders\n";
        cout << "8. Display Products\n";
        cout << "9. Search Products\n";
        cout << "10. Logout\n";
        cout << "11. Exit\n";
    } else if (currentUser->getUserType() == "merchant") {
        cout << "1. Change Password\n";
        cout << "2. Check Balance\n";
        cout << "3. Recharge\n";
        cout << "4. Add Product\n";
        cout << "5. Manage Products\n";
        cout << "6. Display Products\n";
        cout << "7. Search Products\n";
        cout << "8. Apply Discount\n";
        cout << "9. View Received Orders\n";
        cout << "10. Logout\n";
        cout << "11. Exit\n";
    }
    cout << "Please enter your choice: ";

    // 获取用户输入
    cin >> choice;
    if (cin.fail()) {
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        showUserMenu(currentUser);  // 重新显示菜单
        return;
    }
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    // 处理用户选择
    if (currentUser->getUserType() == "consumer") {
        switch (choice) {
            case 1:
                changePasswordScreen(currentUser);
                break;
            case 2:
                checkBalanceScreen(currentUser);
                break;
            case 3:
                rechargeScreen(currentUser);
                break;
            case 4:
                makePurchaseScreen(currentUser);
                break;
            case 5:
                shoppingCartScreen(currentUser);
                break;
            case 6:
                generateOrderScreen(currentUser);
                break;
            case 7:
                viewOrdersScreen(currentUser);
                break;
            case 8:
                displayProductsScreen(currentUser);
                break;
            case 9:
                searchProductsScreen(currentUser);
                break;
            case 10:
                showMainMenu();
                break;
            case 11:
                cout << "Thank you for using our platform. Goodbye!" << endl;
                exit(0);
            default:
                cout << "Invalid choice. Please try again." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                showUserMenu(currentUser);
        }
    } else if (currentUser->getUserType() == "merchant") {
        switch (choice) {
            case 1:
                changePasswordScreen(currentUser);
                break;
            case 2:
                checkBalanceScreen(currentUser);
                break;
            case 3:
                rechargeScreen(currentUser);
                break;
            case 4:
                addProductScreen(currentUser);
                break;
            case 5:
                manageProductsScreen(currentUser);
                break;
            case 6:
                displayProductsScreen(currentUser);
                break;
            case 7:
                searchProductsScreen(currentUser);
                break;
            case 8:
                applyDiscountScreen(currentUser);
                break;
            case 9:
                viewMerchantOrdersScreen(currentUser);
                break;
            case 10:
                showMainMenu();
                break;
            case 11:
                cout << "Thank you for using our platform. Goodbye!" << endl;
                exit(0);
            default:
                cout << "Invalid choice. Please try again." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                showUserMenu(currentUser);
        }
    }
}

// 修改密码界面
void changePasswordScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Change Password =====\n";

    string newPassword;

    while (true) {
        cout << "Please enter your new password (at least 6 characters, must include letters, digits, and symbols)\n";
        cout << "Or type 'exit' to cancel: ";
        getline(cin, newPassword);

        if (newPassword == "exit") {
            cout << "Password change cancelled." << endl;
            cout << "Press Enter to return to user menu...";
            cin.get();
            showUserMenu(currentUser);
            return;
        }

        if (!isPasswordComplex(newPassword)) {
            cout << "Password is not complex enough. Please try again." << endl;
        } else {
            break;
        }
    }

    currentUser->changePassword(newPassword);
    cout << "Password changed successfully!" << endl;

    cout << "\n1. Return to user menu\n";
    cout << "2. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            showUserMenu(currentUser);
            break;
        case 2:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 查询余额界面
void checkBalanceScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Check Balance =====\n";

    cout << "Current balance: " << currentUser->queryBalance() << endl;

    cout << "\n1. Return to user menu\n";
    cout << "2. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            showUserMenu(currentUser);
            break;
        case 2:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 充值界面
void rechargeScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Recharge =====\n";
    cout << "1. Proceed with recharge\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续充值流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    double amount;
    cout << "Please enter the recharge amount: ";
    cin >> amount;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    if (currentUser->recharge(amount)) {
        cout << "Recharge successful!" << endl;
    } else {
        cout << "Invalid recharge amount. Please enter a positive number." << endl;
    }

    cout << "\n1. Recharge again\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            rechargeScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 消费者购买界面
void makePurchaseScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Make Purchase =====\n";
    cout << "1. Proceed with purchase\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续购买流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    if (currentUser->getUserType() == "consumer") {
        string productName;
        int quantity;

        // 显示所有商品
        cout << "Available products:\n";
        showProducts();

        // 获取商品名称
        cout << "\nPlease enter the product name you want to purchase (or type 'back' to return): ";
        getline(cin, productName);

        if (productName == "back") {
            showUserMenu(currentUser);
            return;
        }

        // 获取购买数量
        cout << "Please enter the quantity: ";
        cin >> quantity;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        // 查找商品并计算价格
        ifstream inFile(PRODUCTS_FILE);
        string line;
        bool productFound = false;
        double totalPrice = 0;
        int availableQuantity = 0;
        string productType;

        // 跳过CSV文件头
        if (getline(inFile, line)) {
            // 文件头不处理
        }

        // 查找商品
        while (getline(inFile, line)) {
            vector<string> productData = split(line, ',');
            if (productData.size() >= 5 && productData[0] == productName) {
                double price = stod(productData[2]);
                availableQuantity = stoi(productData[3]);
                productType = productData[4];

                if (quantity <= 0) {
                    cout << "Invalid quantity. Please enter a positive number." << endl;
                    inFile.close();

                    cout << "\n1. Try again\n";
                    cout << "2. Return to user menu\n";
                    cout << "3. Exit\n";
                    cout << "Please enter your choice: ";

                    int choice;
                    cin >> choice;
                    cin.ignore(numeric_limits<streamsize>::max(), '\n');

                    switch (choice) {
                        case 1:
                            makePurchaseScreen(currentUser);
                            return;
                        case 2:
                            showUserMenu(currentUser);
                            return;
                        case 3:
                            cout << "Thank you for using our platform. Goodbye!" << endl;
                            exit(0);
                        default:
                            showUserMenu(currentUser);
                            return;
                    }
                }

                if (quantity > availableQuantity) {
                    cout << "Not enough stock. Available quantity: " << availableQuantity << endl;
                    inFile.close();

                    cout << "\n1. Try again\n";
                    cout << "2. Return to user menu\n";
                    cout << "3. Exit\n";
                    cout << "Please enter your choice: ";

                    int choice;
                    cin >> choice;
                    cin.ignore(numeric_limits<streamsize>::max(), '\n');

                    switch (choice) {
                        case 1:
                            makePurchaseScreen(currentUser);
                            return;
                        case 2:
                            showUserMenu(currentUser);
                            return;
                        case 3:
                            cout << "Thank you for using our platform. Goodbye!" << endl;
                            exit(0);
                        default:
                            showUserMenu(currentUser);
                            return;
                    }
                }

                totalPrice = price * quantity;
                productFound = true;
                break;
            }
        }
        inFile.close();

        if (!productFound) {
            cout << "Product not found." << endl;

            cout << "\n1. Try again\n";
            cout << "2. Return to user menu\n";
            cout << "3. Exit\n";
            cout << "Please enter your choice: ";

            int choice;
            cin >> choice;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            switch (choice) {
                case 1:
                    makePurchaseScreen(currentUser);
                    return;
                case 2:
                    showUserMenu(currentUser);
                    return;
                case 3:
                    cout << "Thank you for using our platform. Goodbye!" << endl;
                    exit(0);
                default:
                    showUserMenu(currentUser);
                    return;
            }
        }

        // 检查余额并扣款
        if (currentUser->consume(totalPrice)) {
            // 更新商品库存
            vector<vector<string>> products;
            ifstream inFile2(PRODUCTS_FILE);

            // 读取CSV文件头
            if (getline(inFile2, line)) {
                products.push_back(split(line, ','));
            }

            // 读取所有产品数据
            while (getline(inFile2, line)) {
                vector<string> productData = split(line, ',');
                if (productData.size() >= 5 && productData[0] == productName) {
                    // 更新库存
                    int newQuantity = availableQuantity - quantity;
                    productData[3] = to_string(newQuantity);
                }
                products.push_back(productData);
            }
            inFile2.close();

            // 写回CSV文件
            ofstream outFile(PRODUCTS_FILE);
            for (const auto& product : products) {
                outFile << join(product, ',') << endl;
            }
            outFile.close();

            cout << "Purchase successful!" << endl;
            cout << "Total cost: " << totalPrice << endl;
            cout << "Remaining balance: " << currentUser->queryBalance() << endl;
        } else {
            cout << "Insufficient balance. Your balance: " << currentUser->queryBalance()
                 << ", Required: " << totalPrice << endl;
        }
    }

    cout << "\n1. Make another purchase\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            makePurchaseScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 商家添加商品界面
void addProductScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Add Product =====\n";
    cout << "1. Proceed with adding product\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续添加商品流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    if (currentUser->getUserType() == "merchant") {
        string name, description, type;
        double price;
        int quantity;

        cout << "Please enter product name (or type 'back' to return): ";
        getline(cin, name);

        if (name == "back") {
            showUserMenu(currentUser);
            return;
        }

        cout << "Please enter product description: ";
        getline(cin, description);
        cout << "Please enter product price: ";
        cin >> price;
        cout << "Please enter product quantity: ";
        cin >> quantity;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        cout << "Available product types:" << endl;
        cout << "1. Book" << endl;
        cout << "2. Food" << endl;
        cout << "3. Daily Necessities" << endl;
        cout << "4. Personal Care" << endl;
        cout << "5. Clothing" << endl;
        cout << "6. Appliances" << endl;
        cout << "7. Stationery and Sports Goods" << endl;
        cout << "8. Toys" << endl;
        cout << "9. Beverages" << endl;
        cout << "10. Maternity and Baby Products" << endl;
        cout << "11. Others" << endl;
        cout << "Please enter product type number: ";

        int typeChoice;
        cin >> typeChoice;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        switch (typeChoice) {
            case 1: type = "Book"; break;
            case 2: type = "Food"; break;
            case 3: type = "Daily Necessities"; break;
            case 4: type = "Personal Care"; break;
            case 5: type = "Clothing"; break;
            case 6: type = "Appliances"; break;
            case 7: type = "Stationery and Sports Goods"; break;
            case 8: type = "Toys"; break;
            case 9: type = "Beverages"; break;
            case 10: type = "Maternity and Baby Products"; break;
            case 11: type = "Others"; break;
            default:
                cout << "Invalid choice. Using 'Others' as default." << endl;
                type = "Others";
        }

        auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
        if (merchant) {
            merchant->addProduct(name, description, price, quantity, type);
            cout << "Product added successfully!" << endl;
        }
    }

    cout << "\n1. Add another product\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            addProductScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 商家管理商品界面
void manageProductsScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Manage Products =====\n";
    cout << "1. Proceed with managing products\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续管理商品流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    if (currentUser->getUserType() == "merchant") {
        string name;
        double price;
        int quantity;

        cout << "Please enter product name to manage (or type 'back' to return): ";
        getline(cin, name);

        if (name == "back") {
            showUserMenu(currentUser);
            return;
        }

        cout << "Please enter new price (0 to keep current): ";
        cin >> price;
        cout << "Please enter new quantity (0 to keep current): ";
        cin >> quantity;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
        if (merchant) {
            double* pricePtr = price > 0 ? &price : nullptr;
            int* quantityPtr = quantity > 0 ? &quantity : nullptr;

            if (merchant->manageProduct(name, pricePtr, quantityPtr)) {
                cout << "Product updated successfully!" << endl;
            } else {
                cout << "Product not found!" << endl;
            }
        }
    }

    cout << "\n1. Manage another product\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            manageProductsScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 商家打折界面
void applyDiscountScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Apply Discount =====\n";
    cout << "1. Proceed with applying discount\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            break; // 继续打折流程
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    if (currentUser->getUserType() == "merchant") {
        cout << "Available product types:" << endl;
        cout << "1. Book" << endl;
        cout << "2. Food" << endl;
        cout << "3. Daily Necessities" << endl;
        cout << "4. Personal Care" << endl;
        cout << "5. Clothing" << endl;
        cout << "6. Appliances" << endl;
        cout << "7. Stationery and Sports Goods" << endl;
        cout << "8. Toys" << endl;
        cout << "9. Beverages" << endl;
        cout << "10. Maternity and Baby Products" << endl;
        cout << "11. Others" << endl;
        cout << "12. Return to user menu" << endl;
        cout << "Please enter product type number to discount: ";

        int typeChoice;
        cin >> typeChoice;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        if (typeChoice == 12) {
            showUserMenu(currentUser);
            return;
        }

        string type;
        switch (typeChoice) {
            case 1: type = "Book"; break;
            case 2: type = "Food"; break;
            case 3: type = "Daily Necessities"; break;
            case 4: type = "Personal Care"; break;
            case 5: type = "Clothing"; break;
            case 6: type = "Appliances"; break;
            case 7: type = "Stationery and Sports Goods"; break;
            case 8: type = "Toys"; break;
            case 9: type = "Beverages"; break;
            case 10: type = "Maternity and Baby Products"; break;
            case 11: type = "Others"; break;
            default:
                cout << "Invalid choice. Operation cancelled." << endl;
                cout << "Press Enter to return to user menu...";
                cin.get();
                showUserMenu(currentUser);
                return;
        }

        double rate;
        cout << "Please enter discount rate (0.1-1.0): ";
        cin >> rate;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        if (rate > 0 && rate <= 1) {
            discountProducts(type, rate);
            cout << "Discount applied successfully!" << endl;
        } else {
            cout << "Invalid discount rate!" << endl;
        }
    }

    cout << "\n1. Apply another discount\n";
    cout << "2. Return to user menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1:
            applyDiscountScreen(currentUser);
            break;
        case 2:
            showUserMenu(currentUser);
            break;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
    }
}

// 购物车界面实现
void shoppingCartScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Shopping Cart =====\n";

    // 从文件中读取购物车数据
    string cartFile = currentUser->getUserName() + "_cart.csv";
    ifstream inFile(cartFile);
    bool cartExists = inFile.good();
    vector<vector<string>> cartItems;

    if (cartExists) {
        string line;
        // 读取CSV文件头
        if (getline(inFile, line)) {
            cartItems.push_back(split(line, ','));
        }

        // 读取所有购物车项目
        double totalAmount = 0;
        cout << "Your cart contains:\n";
        int itemIndex = 1;

        while (getline(inFile, line)) {
            vector<string> itemData = split(line, ',');
            if (itemData.size() >= 4) {
                string name = itemData[0];
                string price = itemData[1];
                string quantity = itemData[2];
                string type = itemData[3];

                cout << itemIndex << ". " << name << " - Price: " << price
                     << " - Quantity: " << quantity << " - Type: " << type << endl;

                totalAmount += stod(price) * stoi(quantity);
                itemIndex++;
            }
            cartItems.push_back(itemData);
        }

        if (itemIndex == 1) {
            cout << "Your cart is empty." << endl;
        } else {
            cout << "\nTotal amount: " << totalAmount << endl;
        }
    } else {
        cout << "Your cart is empty." << endl;
    }
    inFile.close();

    cout << "\n1. Add item to cart\n";
    cout << "2. Remove item from cart\n";
    cout << "3. Modify item quantity\n"; // 新增修改数量选项
    cout << "4. Generate order\n"; // 新增生成订单选项
    cout << "5. View orders\n";    // 新增查看订单选项
    cout << "6. Return to user menu\n";
    cout << "7. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1: {
            // 添加商品到购物车
            cout << "\nAvailable products:\n";
            showProducts();

            string productName;
            cout << "\nPlease enter the product name you want to add (or type 'back' to return): ";
            getline(cin, productName);

            if (productName == "back") {
                shoppingCartScreen(currentUser);
                return;
            }

            // 查找商品
            ifstream prodFile(PRODUCTS_FILE);
            string line;
            bool productFound = false;
            string productPrice, productType;
            int availableQuantity = 0;

            // 跳过CSV文件头
            if (getline(prodFile, line)) {
                // 文件头不处理
            }

            while (getline(prodFile, line)) {
                vector<string> productData = split(line, ',');
                if (productData.size() >= 6 && productData[0] == productName) {  // 确保至少有6个字段
                    productFound = true;
                    productPrice = productData[2];
                    availableQuantity = stoi(productData[3]);
                    productType = productData[4];
                    string merchantName = productData[5];  // 获取商家名称

                    // 不要在这里添加到购物车，只获取商品信息
                    break;
                }
            }
            prodFile.close();

            if (!productFound) {
                cout << "Product not found." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen(currentUser);
                return;
            }

            // 获取购买数量
            int quantity;
            cout << "Please enter the quantity: ";
            cin >> quantity;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            if (quantity <= 0 || quantity > availableQuantity) {
                cout << "Invalid quantity. Available: " << availableQuantity << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen(currentUser);
                return;
            }

            // 添加到购物车文件
            if (!cartExists) {
                ofstream createFile(cartFile);
                createFile << "name,price,quantity,type,merchant" << endl;
                createFile.close();
                cartItems.push_back({"name", "price", "quantity", "type", "merchant"});
            }

            // 检查购物车中是否已有该商品
            bool itemExists = false;
            for (size_t i = 1; i < cartItems.size(); i++) {
                if (cartItems[i].size() >= 4 && cartItems[i][0] == productName) {
                    itemExists = true;
                    int newQuantity = stoi(cartItems[i][2]) + quantity;
                    if (newQuantity > availableQuantity) {
                        cout << "Cannot add more. Available: " << availableQuantity << endl;
                        cout << "Press Enter to continue...";
                        cin.get();
                        shoppingCartScreen(currentUser);
                        return;
                    }
                    cartItems[i][2] = to_string(newQuantity);
                    break;
                }
            }

            // 如果购物车中没有该商品，则添加
            if (!itemExists) {
                // 从之前查询到的商品信息中获取商家名称
                string merchantName = "";
                ifstream prodFile2(PRODUCTS_FILE);
                string line2;

                // 跳过CSV文件头
                if (getline(prodFile2, line2)) {
                    // 文件头不处理
                }

                while (getline(prodFile2, line2)) {
                    vector<string> productData = split(line2, ',');
                    if (productData.size() >= 6 && productData[0] == productName) {
                        merchantName = productData[5];
                        break;
                    }
                }
                prodFile2.close();

                cartItems.push_back({productName, productPrice, to_string(quantity), productType, merchantName});
            }

            // 写回购物车文件
            ofstream outFile(cartFile);
            for (const auto& item : cartItems) {
                outFile << join(item, ',') << endl;
            }
            outFile.close();

            cout << "Item added to cart successfully!" << endl;
            break;
        }
        case 2: {
            // 从购物车移除商品
            if (!cartExists || cartItems.size() <= 1) {
                cout << "Your cart is empty." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen(currentUser);
                return;
            }

            int itemIndex;
            cout << "Enter the item number to remove: ";
            cin >> itemIndex;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            if (itemIndex < 1 || itemIndex >= static_cast<int>(cartItems.size())) {
                cout << "Invalid item number." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen(currentUser);
                return;
            }

            cartItems.erase(cartItems.begin() + itemIndex);

            // 写回购物车文件
            ofstream outFile(cartFile);
            for (const auto& item : cartItems) {
                outFile << join(item, ',') << endl;
            }
            outFile.close();

            cout << "Item removed from cart successfully!" << endl;
            break;
        }
        case 3: {
            // 修改购物车中商品数量
            if (!cartExists || cartItems.size() <= 1) {
                cout << "Your cart is empty." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen(currentUser);
                return;
            }

            int itemIndex;
            cout << "Enter the item number to modify: ";
            cin >> itemIndex;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            if (itemIndex < 1 || itemIndex >= static_cast<int>(cartItems.size())) {
                cout << "Invalid item number." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen(currentUser);
                return;
            }

            // 获取商品名称和当前数量
            string productName = cartItems[itemIndex][0];
            int currentQuantity = stoi(cartItems[itemIndex][2]);

            // 查询商品可用库存
            ifstream prodFile(PRODUCTS_FILE);
            string line;
            int availableQuantity = 0;

            // 跳过CSV文件头
            if (getline(prodFile, line)) {
                // 文件头不处理
            }

            while (getline(prodFile, line)) {
                vector<string> productData = split(line, ',');
                if (productData.size() >= 5 && productData[0] == productName) {
                    availableQuantity = stoi(productData[3]);
                    break;
                }
            }
            prodFile.close();

            // 获取新数量
            int newQuantity;
            cout << "Current quantity: " << currentQuantity << endl;
            cout << "Available in stock: " << availableQuantity << endl;
            cout << "Enter new quantity (1-" << availableQuantity << "): ";
            cin >> newQuantity;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            if (newQuantity <= 0) {
                cout << "Invalid quantity. Quantity must be positive." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen(currentUser);
                return;
            }

            if (newQuantity > availableQuantity) {
                cout << "Not enough stock available." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen(currentUser);
                return;
            }

            // 更新购物车中的数量
            cartItems[itemIndex][2] = to_string(newQuantity);

            // 写回购物车文件
            ofstream outFile(cartFile);
            for (const auto& item : cartItems) {
                outFile << join(item, ',') << endl;
            }
            outFile.close();

            cout << "Item quantity updated successfully!" << endl;
            break;
        }
        case 4: {
            // 生成订单
            generateOrderScreen(currentUser);
            return;
        }
        case 5: {
            // 查看订单
            viewOrdersScreen(currentUser);
            return;
        }
        case 6: {
            showUserMenu(currentUser);
            return;
        }
        case 7: {
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        }
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }

    cout << "Press Enter to continue...";
    cin.get();
    shoppingCartScreen(currentUser);
}

// 查看订单界面
void viewOrdersScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== My Orders =====\n";

    string ordersFile = "orders.csv";
    ifstream inFile(ordersFile);

    if (!inFile.is_open()) {
        cout << "No orders found." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen(currentUser);
        return;
    }

    string line;
    vector<vector<string>> orders;

    // 读取CSV文件头
    if (getline(inFile, line)) {
        orders.push_back(split(line, ','));
    }

    // 读取所有订单
    while (getline(inFile, line)) {
        vector<string> orderData = split(line, ',');
        if (orderData.size() >= 5 && orderData[1] == currentUser->getUserName()) {
            orders.push_back(orderData);
        }
    }
    inFile.close();

    if (orders.size() <= 1) {
        cout << "No orders found." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen(currentUser);
        return;
    }

    // 显示订单列表
    cout << "Your orders:\n";
    for (size_t i = 1; i < orders.size(); i++) {
        cout << i << ". Order ID: " << orders[i][0]
             << " - Date: " << orders[i][2]
             << " - Status: " << orders[i][3]
             << " - Total: " << orders[i][4] << endl;
    }

    cout << "\n1. Pay for an order\n";
    cout << "2. View order details\n";
    cout << "3. Return to shopping cart\n";
    cout << "4. Return to user menu\n";
    cout << "5. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1: {
            // 支付订单
            int orderIndex;
            cout << "Enter the order number to pay: ";
            cin >> orderIndex;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            if (orderIndex < 1 || orderIndex >= static_cast<int>(orders.size())) {
                cout << "Invalid order number." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen(currentUser);
                return;
            }

            if (orders[orderIndex][3] != "unpaid") {
                cout << "This order is already " << orders[orderIndex][3] << "." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen(currentUser);
                return;
            }

            payOrderScreen(currentUser, orders[orderIndex][0]);
            return;
        }
        case 2: {
            // 查看订单详情
            int orderIndex;
            cout << "Enter the order number to view: ";
            cin >> orderIndex;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            if (orderIndex < 1 || orderIndex >= static_cast<int>(orders.size())) {
                cout << "Invalid order number." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen(currentUser);
                return;
            }

            string orderId = orders[orderIndex][0];

            // 简化版本：直接显示订单基本信息
            cout << "\n===== Order Details =====\n";
            cout << "Order ID: " << orderId << endl;
            cout << "Date: " << orders[orderIndex][2] << endl;
            cout << "Status: " << orders[orderIndex][3] << endl;
            cout << "Total: " << orders[orderIndex][4] << endl;
            cout << "\nNote: Detailed item information is no longer stored in separate files." << endl;



            cout << "\nPress Enter to continue...";
            cin.get();
            viewOrdersScreen(currentUser);
            return;
        }
        case 3:
            shoppingCartScreen(currentUser);
            return;
        case 4:
            showUserMenu(currentUser);
            return;
        case 5:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to orders screen..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            viewOrdersScreen(currentUser);
            return;
    }
}

// 生成订单界面
void generateOrderScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Generate Order =====\n";

    // 从文件中读取购物车数据
    string cartFile = currentUser->getUserName() + "_cart.csv";
    ifstream inFile(cartFile);
    bool cartExists = inFile.good();
    vector<vector<string>> cartItems;

    if (!cartExists || !inFile.is_open()) {
        cout << "Your cart is empty." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen(currentUser);
        return;
    }

    string line;
    // 读取CSV文件头
    if (getline(inFile, line)) {
        cartItems.push_back(split(line, ','));
    }

    // 读取所有购物车项目
    while (getline(inFile, line)) {
        vector<string> itemData = split(line, ',');
        if (itemData.size() >= 4) {
            cartItems.push_back(itemData);
        }
    }
    inFile.close();

    if (cartItems.size() <= 1) {
        cout << "Your cart is empty." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen(currentUser);
        return;
    }

    // 显示购物车中的商品
    cout << "Your cart contains:\n";
    vector<bool> selectedItems(cartItems.size(), false);
    selectedItems[0] = true; // 标题行默认选中

    for (size_t i = 1; i < cartItems.size(); i++) {
        if (cartItems[i].size() >= 4) {
            cout << i << ". " << cartItems[i][0] << " - Price: " << cartItems[i][1]
                 << " - Quantity: " << cartItems[i][2] << " - Type: " << cartItems[i][3] << endl;
        }
    }

    // 选择要生成订单的商品
    cout << "\nSelect items to include in the order (enter item numbers separated by spaces, 'all' for all items, or 'back' to return): ";
    string selection;
    getline(cin, selection);

    // 检查是否要返回
    if (selection == "back") {
        cout << "Returning to shopping cart..." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen(currentUser);
        return;
    }

    if (selection == "all") {
        for (size_t i = 0; i < selectedItems.size(); i++) {
            selectedItems[i] = true;
        }
    } else {
        istringstream iss(selection);
        int itemNum;
        while (iss >> itemNum) {
            if (itemNum > 0 && itemNum < static_cast<int>(cartItems.size())) {
                selectedItems[itemNum] = true;
            }
        }
    }

    // 检查是否有选中的商品
    bool hasSelected = false;
    for (size_t i = 1; i < selectedItems.size(); i++) {
        if (selectedItems[i]) {
            hasSelected = true;
            break;
        }
    }

    if (!hasSelected) {
        cout << "No items selected." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen(currentUser);
        return;
    }

    // 检查库存是否足够
    bool stockSufficient = true;
    for (size_t i = 1; i < cartItems.size(); i++) {
        if (selectedItems[i] && cartItems[i].size() >= 4) {
            string productName = cartItems[i][0];
            int quantity = stoi(cartItems[i][2]);

            // 查询商品可用库存
            ifstream prodFile(PRODUCTS_FILE);
            string line;
            int availableQuantity = 0;

            // 跳过CSV文件头
            if (getline(prodFile, line)) {
                // 文件头不处理
            }

            while (getline(prodFile, line)) {
                vector<string> productData = split(line, ',');
                if (productData.size() >= 5 && productData[0] == productName) {
                    availableQuantity = stoi(productData[3]);
                    break;
                }
            }
            prodFile.close();

            if (quantity > availableQuantity) {
                cout << "Not enough stock for " << productName << ". Available: " << availableQuantity << endl;
                stockSufficient = false;
            }
        }
    }

    if (!stockSufficient) {
        cout << "Cannot generate order due to insufficient stock." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen(currentUser);
        return;
    }

    // 计算订单总金额
    double totalAmount = 0;
    vector<vector<string>> orderItems;
    orderItems.push_back({"name", "price", "quantity", "type", "merchant"});

    for (size_t i = 1; i < cartItems.size(); i++) {
        if (selectedItems[i] && cartItems[i].size() >= 4) {
            string productName = cartItems[i][0];
            double price = stod(cartItems[i][1]);
            int quantity = stoi(cartItems[i][2]);
            string type = cartItems[i][3];

            // 查找商品所属商家
            string merchant = "";
            ifstream prodFile(PRODUCTS_FILE);
            string line;

            // 跳过CSV文件头
            if (getline(prodFile, line)) {
                // 文件头不处理
            }

            while (getline(prodFile, line)) {
                vector<string> productData = split(line, ',');
                if (productData.size() >= 6 && productData[0] == productName) {
                    merchant = productData.size() >= 6 ? productData[5] : "unknown";
                    break;
                }
            }
            prodFile.close();

            totalAmount += price * quantity;
            vector<string> orderItem = {productName, to_string(price), to_string(quantity), type, merchant};
            orderItems.push_back(orderItem);
        }
    }

    // 生成订单ID (时间戳 + 用户名)
    string orderId = to_string(time(nullptr)) + "_" + currentUser->getUserName();

    // 显示订单信息
    cout << "\n===== Order Summary =====\n";
    cout << "Order ID: " << orderId << endl;
    cout << "Items:\n";

    for (size_t i = 1; i < orderItems.size(); i++) {
        cout << "- " << orderItems[i][0] << " (" << orderItems[i][1] << ") " <<" x" << orderItems[i][2]
             << " = " << stod(orderItems[i][1]) * stoi(orderItems[i][2]) << endl;
    }

    cout << "\nTotal Amount: " << totalAmount << endl;
    cout << "Current Balance: " << currentUser->queryBalance() << endl;

    // 确认生成订单
    cout << "\nGenerate this order? (y/n): ";
    char confirm;
    cin >> confirm;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    if (tolower(confirm) != 'y') {
        cout << "Order generation cancelled." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen(currentUser);
        return;
    }

    // 冻结商品库存
    for (size_t i = 1; i < orderItems.size(); i++) {
        string productName = orderItems[i][0];
        int quantity = stoi(orderItems[i][2]);

        // 读取所有产品
        vector<vector<string>> products;
        ifstream prodFile(PRODUCTS_FILE);
        string line;

        // 读取CSV文件头
        if (getline(prodFile, line)) {
            products.push_back(split(line, ','));
        }

        // 读取所有产品数据
        while (getline(prodFile, line)) {
            vector<string> productData = split(line, ',');
            if (productData.size() >= 5 && productData[0] == productName) {
                // 更新库存 (冻结)
                int newQuantity = stoi(productData[3]) - quantity;
                productData[3] = to_string(newQuantity);
            }
            products.push_back(productData);
        }
        prodFile.close();

        // 写回CSV文件
        ofstream outFile(PRODUCTS_FILE);
        for (const auto& product : products) {
            outFile << join(product, ',') << endl;
        }
        outFile.close();
    }

    // 保存订单到文件
    string ordersFile = "orders.csv";
    bool ordersFileExists = ifstream(ordersFile).good();

    ofstream outOrdersFile(ordersFile, ios::app);
    if (!ordersFileExists) {
        outOrdersFile << "id,user,date,status,total" << endl;
    }

    // 获取当前时间
    time_t now = time(nullptr);
    char timeBuffer[80];
    strftime(timeBuffer, sizeof(timeBuffer), "%Y-%m-%d %H:%M:%S", localtime(&now));

    // 使用固定格式保存金额，确保使用点号作为小数分隔符
    outOrdersFile << orderId << "," << currentUser->getUserName() << ","
                 << timeBuffer << ",unpaid," << fixed << setprecision(2) << totalAmount << endl;
    outOrdersFile.close();

    // 将订单信息添加到对应商家的订单文件中
    // 按商家分组计算订单金额
    map<string, double> merchantOrderAmounts;
    for (size_t i = 1; i < orderItems.size(); i++) {
        if (orderItems[i].size() >= 5) {
            string merchantName = orderItems[i][4];
            double price = stod(orderItems[i][1]);
            int quantity = stoi(orderItems[i][2]);
            double itemTotal = price * quantity;

            if (merchantOrderAmounts.find(merchantName) == merchantOrderAmounts.end()) {
                merchantOrderAmounts[merchantName] = 0;
            }
            merchantOrderAmounts[merchantName] += itemTotal;
        }
    }

    // 为每个商家添加订单记录
    auto users = loadUsers();
    for (const auto& merchantOrder : merchantOrderAmounts) {
        string merchantName = merchantOrder.first;
        double merchantAmount = merchantOrder.second;

        // 查找商家用户
        for (auto& user : users) {
            if (user->getUserName() == merchantName && user->getUserType() == "merchant") {
                // 添加订单记录到商家的订单文件
                auto merchant = dynamic_pointer_cast<Merchant>(user);
                if (merchant) {
                    merchant->receiveOrder(orderId, currentUser->getUserName(), timeBuffer, merchantAmount);
                }
                break;
            }
        }
    }

    // 不再创建单独的订单详情文件
    // 订单详情将存储在用户总订单文件中

    // 从购物车中移除已生成订单的商品
    vector<vector<string>> remainingItems;
    remainingItems.push_back(cartItems[0]); // 保留标题行

    for (size_t i = 1; i < cartItems.size(); i++) {
        if (!selectedItems[i]) {
            remainingItems.push_back(cartItems[i]);
        }
    }

    // 更新购物车文件
    ofstream outCartFile(cartFile);
    for (const auto& item : remainingItems) {
        outCartFile << join(item, ',') << endl;
    }
    outCartFile.close();

    cout << "Order generated successfully! Order ID: " << orderId << endl;
    cout << "Press Enter to continue...";
    cin.get();

    // 询问是否立即支付
    cout << "Would you like to pay for this order now? (y/n): ";
    cin >> confirm;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    if (tolower(confirm) == 'y') {
        payOrderScreen(currentUser, orderId);
    } else {
        viewOrdersScreen(currentUser);
    }
}

// 支付订单界面
void payOrderScreen(shared_ptr<User> currentUser, const string& orderId) {
    system("cls");
    cout << "\n===== Pay Order =====\n";
    cout << "Order ID: " << orderId << endl;

    // 查找订单信息
    string ordersFile = "orders.csv";
    ifstream inFile(ordersFile);

    if (!inFile.is_open()) {
        cout << "Order not found." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen(currentUser);
        return;
    }

    string line;
    vector<vector<string>> orders;
    bool orderFound = false;
    int orderIndex = -1;

    // 读取CSV文件头
    if (getline(inFile, line)) {
        orders.push_back(split(line, ','));
    }

    // 读取所有订单
    while (getline(inFile, line)) {
        vector<string> orderData = split(line, ',');
        orders.push_back(orderData);
        if (orderData.size() >= 5 && orderData[0] == orderId) {
            orderFound = true;
            orderIndex = orders.size() - 1;
        }
    }
    inFile.close();

    if (!orderFound) {
        cout << "Order not found." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen(currentUser);
        return;
    }

    if (orders[orderIndex][3] != "unpaid") {
        cout << "This order is already " << orders[orderIndex][3] << "." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen(currentUser);
        return;
    }

    if (orders[orderIndex].size() < 5) {
        cout << "Error: Order data incomplete" << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen(currentUser);
        return;
    }

    string totalAmountStr = orders[orderIndex][4];

    // 清理字符串中的空白字符
    totalAmountStr.erase(0, totalAmountStr.find_first_not_of(" \t\r\n"));
    totalAmountStr.erase(totalAmountStr.find_last_not_of(" \t\r\n") + 1);

    double totalAmount;
    try {
        totalAmount = stod(totalAmountStr);
    } catch (const std::exception& e) {
        cout << "Error: Invalid order amount: '" << totalAmountStr << "'" << endl;
        cout << "Exception: " << e.what() << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen(currentUser);
        return;
    }

    // 检查余额
    if (currentUser->queryBalance() < totalAmount) {
        cout << "Insufficient balance. Please recharge." << endl;
        cout << "Current balance: " << currentUser->queryBalance() << endl;
        cout << "Required amount: " << totalAmount << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen(currentUser);
        return;
    }

    // 输入密码确认
    string password;
    cout << "Please enter your password to confirm payment: ";
    getline(cin, password);

    if (!currentUser->verifyPassword(password)) {
        cout << "Incorrect password. Payment cancelled." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen(currentUser);
        return;
    }

    // 简化版本：不再读取详细的订单项目信息
    // 直接进行支付处理

    // 扣除消费者余额
    if (!currentUser->consume(totalAmount)) {
        cout << "Payment failed. Please try again later." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen(currentUser);
        return;
    }

    // 支付成功后，需要将金额转入对应的商家账户并更新商家订单状态
    // 查找所有商家用户，检查他们的订单文件中是否有这个订单
    auto users = loadUsers();
    for (auto& user : users) {
        if (user->getUserType() == "merchant") {
            auto merchant = dynamic_pointer_cast<Merchant>(user);
            if (merchant) {
                string merchantOrdersFile = user->getUserName() + "_orders.csv";
                ifstream checkFile(merchantOrdersFile);

                if (checkFile.good()) {
                    string line;
                    bool hasThisOrder = false;
                    double merchantAmount = 0.0;

                    // 跳过CSV文件头
                    if (getline(checkFile, line)) {
                        // 文件头不处理
                    }

                    // 查找是否有这个订单
                    while (getline(checkFile, line)) {
                        vector<string> orderData = split(line, ',');
                        if (orderData.size() >= 4 && orderData[0] == orderId) {
                            hasThisOrder = true;
                            merchantAmount = stod(orderData[3]);
                            break;
                        }
                    }
                    checkFile.close();

                    if (hasThisOrder) {
                        // 增加商家余额
                        user->recharge(merchantAmount);

                        // 更新商家订单状态
                        merchant->updateOrderStatus(orderId, "paid");
                    }
                }
            }
        }
    }

    // 更新订单状态
    orders[orderIndex][3] = "paid";

    // 写回订单文件
    ofstream outFile(ordersFile);
    for (const auto& order : orders) {
        outFile << join(order, ',') << endl;
    }
    outFile.close();

    cout << "Payment successful! Thank you for your purchase." << endl;
    cout << "Press Enter to continue...";
    cin.get();
    viewOrdersScreen(currentUser);
}

// 添加商家查看接收到的订单的界面函数
void viewMerchantOrdersScreen(shared_ptr<User> currentUser) {
    system("cls");
    cout << "\n===== Received Orders =====\n";

    string merchantOrdersFile = currentUser->getUserName() + "_orders.csv";
    ifstream inFile(merchantOrdersFile);

    if (!inFile.is_open()) {
        cout << "No orders received yet." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        showUserMenu(currentUser);
        return;
    }

    string line;
    vector<vector<string>> orders;

    // 读取CSV文件头
    if (std::getline(inFile, line)) {
        orders.push_back(split(line, ','));
    }

    // 读取所有订单
    while (std::getline(inFile, line)) {
        orders.push_back(split(line, ','));
    }
    inFile.close();

    if (orders.size() <= 1) {
        cout << "No orders received yet." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        showUserMenu(currentUser);
        return;
    }

    // 计算每笔订单后的累计余额
    double runningBalance = 0;
    vector<double> balanceAfterOrder;

    // 首先获取用户的初始余额（从用户文件中读取）
    auto users = loadUsers();
    for (auto& user : users) {
        if (user->getUserName() == currentUser->getUserName()) {
            runningBalance = user->queryBalance();
            break;
        }
    }

    // 计算每笔订单前的余额
    for (size_t i = orders.size() - 1; i >= 1; i--) {
        if (orders[i].size() >= 4) {
            string amountStr = orders[i][3];
            // 清理字符串中的空白字符
            amountStr.erase(0, amountStr.find_first_not_of(" \t\r\n"));
            amountStr.erase(amountStr.find_last_not_of(" \t\r\n") + 1);
            double orderAmount = stod(amountStr);
            runningBalance -= orderAmount;
            balanceAfterOrder.insert(balanceAfterOrder.begin(), runningBalance + orderAmount);
        }
        if (i == 1) break; // 防止size_t溢出
    }

    // 显示订单列表
    cout << "\nOrders received:\n";
    for (size_t i = 1; i < orders.size(); i++) {
        if (orders[i].size() >= 4) {
            string status = orders[i].size() >= 5 ? orders[i][4] : "unknown";
            cout << i << ". Order ID: " << orders[i][0]
                 << " - Consumer: " << orders[i][1]
                 << " - Date: " << orders[i][2]
                 << " - Amount: " << orders[i][3]
                 << " - Status: " << status
                 << " - Balance: " << balanceAfterOrder[i-1] << endl;
        }
    }

    cout << "\n1. View Order Details\n";
    cout << "2. Return to User Menu\n";
    cout << "3. Exit\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    switch (choice) {
        case 1: {
            // 查看订单详情
            int orderIndex;
            cout << "Enter the order number to view: ";
            cin >> orderIndex;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');

            if (orderIndex < 1 || orderIndex >= static_cast<int>(orders.size())) {
                cout << "Invalid order number." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewMerchantOrdersScreen(currentUser);
                return;
            }

            string orderId = orders[orderIndex][0];
            viewMerchantOrderDetailsScreen(currentUser, orderId);
            return;
        }
        case 2:
            showUserMenu(currentUser);
            return;
        case 3:
            cout << "Thank you for using our platform. Goodbye!" << endl;
            exit(0);
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            showUserMenu(currentUser);
            return;
    }
}

// 添加商家查看订单详情的界面函数
void viewMerchantOrderDetailsScreen(shared_ptr<User> currentUser, const string& orderId) {
    system("cls");
    cout << "\n===== Order Details =====\n";
    cout << "Order ID: " << orderId << endl;

    // 查找订单信息
    string ordersFile = "orders.csv";
    ifstream inFile(ordersFile);

    if (!inFile.is_open()) {
        cout << "Order not found." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewMerchantOrdersScreen(currentUser);
        return;
    }

    string line;
    vector<vector<string>> orders;
    bool orderFound = false;
    int orderIndex = -1;

    // 读取CSV文件头
    if (std::getline(inFile, line)) {
        orders.push_back(split(line, ','));
    }

    // 读取所有订单
    while (std::getline(inFile, line)) {
        vector<string> orderData = split(line, ',');
        orders.push_back(orderData);
        if (orderData.size() >= 5 && orderData[0] == orderId) {
            orderFound = true;
            orderIndex = orders.size() - 1;
        }
    }
    inFile.close();

    if (!orderFound) {
        cout << "Order not found." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewMerchantOrdersScreen(currentUser);
        return;
    }

    // 显示订单基本信息
    cout << "Date: " << orders[orderIndex][2] << endl;
    cout << "Status: " << orders[orderIndex][3] << endl;
    cout << "Total: " << orders[orderIndex][4] << endl;

    // 简化版本：不再显示详细的订单项目信息
    cout << "\nNote: Detailed item information is no longer stored in separate files." << endl;

    cout << "\nPress Enter to continue...";
    cin.get();
    viewMerchantOrdersScreen(currentUser);
}

int main() {
    initProducts();
    showMainMenu();
    return 0;
}


