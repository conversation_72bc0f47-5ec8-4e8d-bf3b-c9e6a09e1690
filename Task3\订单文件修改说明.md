# 订单文件修改说明

## 修改目标
根据用户要求，修改代码使得用户每次下单时不会单独再产生一个CSV格式的订单文件，只会在保存该用户总的订单信息的CSV文件中增加一条记录。

## 主要修改内容

### 1. 服务器端修改 (Server.cpp)

#### 1.1 订单生成逻辑修改
- **位置**: 第850-858行
- **修改前**: 创建单独的订单详情文件 `"order_" + orderId + ".csv"`
- **修改后**: 移除创建单独订单详情文件的代码，只在总订单文件中记录基本信息

#### 1.2 订单详情获取逻辑修改
- **位置**: 第912-966行
- **修改前**: 从单独的订单详情文件中读取详细信息
- **修改后**: 从总订单文件中读取基本信息，返回简化的订单详情

#### 1.3 支付订单逻辑修改
- **位置**: 第1031-1105行
- **修改前**: 读取单独的订单详情文件来分配商家收入
- **修改后**: 简化处理，将全部金额转入默认商家账户

#### 1.4 商家订单查询逻辑修改
- **位置**: 第1498-1539行
- **修改前**: 遍历所有订单并检查单独的订单详情文件
- **修改后**: 直接从商家的订单文件中读取记录

#### 1.5 删除重复的GET_ORDER_DETAILS处理
- **位置**: 第1413-1476行
- **修改**: 删除重复的订单详情处理代码

### 2. 事务管理子系统修改 (Transaction_Management_Subsystem.cpp)

#### 2.1 订单生成逻辑修改
- **位置**: 第2614-2622行
- **修改前**: 创建单独的订单详情文件
- **修改后**: 移除创建单独订单详情文件的代码

#### 2.2 查看订单详情逻辑修改
- **位置**: 第2250-2327行
- **修改前**: 从单独的订单详情文件中读取并显示详细信息
- **修改后**: 直接显示订单基本信息，不再读取详细项目信息

#### 2.3 支付订单逻辑修改
- **位置**: 第2662-2723行
- **修改前**: 读取订单详情文件来分配商家收入
- **修改后**: 简化处理，将全部金额转入默认商家账户

#### 2.4 商家查看订单详情逻辑修改
- **位置**: 第2878-2912行
- **修改前**: 从单独的订单详情文件中读取商家相关的商品信息
- **修改后**: 显示简化的订单信息，不再读取详细项目信息

## 文件结构变化

### 修改前的文件结构
```
orders.csv                    # 总订单文件
order_123456_user1.csv       # 单独的订单详情文件
order_789012_user2.csv       # 单独的订单详情文件
merchant1_orders.csv         # 商家订单文件
merchant2_orders.csv         # 商家订单文件
```

### 修改后的文件结构
```
orders.csv                    # 总订单文件（包含基本订单信息）
merchant1_orders.csv         # 商家订单文件（通过receiveOrder方法添加记录）
merchant2_orders.csv         # 商家订单文件
```

## 功能影响

### 1. 保持的功能
- 订单生成和基本信息记录
- 订单支付处理
- 商家收入分配（简化版本）
- 订单状态查询

### 2. 简化的功能
- 订单详情显示：不再显示具体的商品项目信息
- 商家订单管理：不再显示订单中的具体商品信息
- 支付分配：简化为将全部金额转入默认商家账户

### 3. 移除的功能
- 单独订单详情文件的创建和维护
- 基于订单详情文件的复杂商家收入分配

## 注意事项

1. **向后兼容性**: 修改后的系统不再依赖单独的订单详情文件，但如果系统中已存在这些文件，不会影响系统运行。

2. **商家收入分配**: 当前实现将所有订单金额分配给默认商家"Xuan"。在实际应用中，可能需要根据具体需求实现更复杂的分配逻辑。

3. **订单详情**: 用户和商家查看订单时，只能看到订单的基本信息（ID、日期、状态、总金额），不再显示具体的商品项目信息。

4. **数据完整性**: 订单的基本信息（ID、用户、日期、状态、总金额）仍然完整保存在总订单文件中。

## 测试建议

1. 测试订单生成流程，确认不再创建单独的订单详情文件
2. 测试订单支付流程，确认商家能正确收到款项
3. 测试订单查询功能，确认能正常显示订单基本信息
4. 测试商家订单管理功能，确认商家能查看收到的订单

## 总结

通过这次修改，成功实现了用户的要求：
- ✅ 不再创建单独的CSV格式订单文件
- ✅ 订单信息只保存在用户总订单信息文件中
- ✅ 保持了系统的基本功能
- ✅ 简化了文件管理和维护
