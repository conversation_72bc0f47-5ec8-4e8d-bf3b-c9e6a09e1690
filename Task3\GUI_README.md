# Shopping Platform GUI Client

## 概述

这是一个基于Windows API的图形化购物平台客户端，提供了直观的用户界面来访问购物平台的各种功能。

## 文件说明

- `ClientGUI.cpp` - 基础版GUI客户端
- `ClientGUI_Enhanced.cpp` - 增强版GUI客户端（推荐使用）
- `compile_gui.bat` - 编译脚本

## 功能特性

### 基础版功能 (ClientGUI.cpp)
- 用户登录界面
- 主菜单导航
- 商品浏览
- 购物车管理
- 余额查询

### 增强版功能 (ClientGUI_Enhanced.cpp)
- 所有基础功能
- 实时余额显示
- 订单管理
- 充值功能
- 改进的用户界面
- 更好的错误处理
- 数量选择功能

## 编译和运行

### 前提条件
- Windows 操作系统
- MinGW-w64 或 Visual Studio
- 确保服务器程序正在运行

### 编译方法

#### 方法1：使用批处理脚本
```bash
# 双击运行 compile_gui.bat
# 或在命令行中执行：
compile_gui.bat
```

#### 方法2：手动编译
```bash
# 编译基础版
g++ -std=c++17 -mwindows -o ClientGUI.exe ClientGUI.cpp -lws2_32 -lcomctl32

# 编译增强版（推荐）
g++ -std=c++17 -mwindows -o ClientGUI_Enhanced.exe ClientGUI_Enhanced.cpp -lws2_32 -lcomctl32
```

### 运行程序
1. 确保服务器程序 `Server.exe` 正在运行
2. 双击运行 `ClientGUI.exe` 或 `ClientGUI_Enhanced.exe`
3. 在登录界面输入用户名和密码

## 使用说明

### 登录
- 输入已注册的用户名和密码
- 点击"Login"按钮登录
- 如需注册新用户，请使用控制台版客户端

### 主菜单功能

#### Browse Products（浏览商品）
- 查看所有可用商品
- 选择商品并指定数量
- 添加商品到购物车

#### Shopping Cart（购物车）
- 查看购物车中的商品
- 移除不需要的商品
- 生成订单

#### My Orders（我的订单）
- 查看历史订单
- 支付未付款订单
- 查看订单详情

#### Check Balance（查询余额）
- 查看当前账户余额

#### Recharge（充值）
- 为账户充值

#### Logout（登出）
- 安全退出系统

## 界面特点

### 用户友好
- 直观的图形界面
- 清晰的按钮布局
- 实时状态更新

### 功能完整
- 涵盖购物平台的主要功能
- 支持商品浏览和购买
- 完整的订单管理流程

### 错误处理
- 网络连接错误提示
- 输入验证
- 友好的错误消息

## 技术特性

### 网络通信
- 基于TCP Socket通信
- 与服务器实时数据交换
- 自动重连机制

### 界面技术
- Windows API原生界面
- 响应式布局
- 多窗口管理

### 数据处理
- CSV数据解析
- 实时数据更新
- 本地数据缓存

## 注意事项

1. **服务器连接**：确保服务器程序在运行GUI客户端之前启动
2. **网络设置**：默认连接到 127.0.0.1:8888，如需修改请编辑源代码
3. **用户注册**：GUI版本暂不支持用户注册，请使用控制台版本注册新用户
4. **数据同步**：多个客户端同时使用时，数据会实时同步

## 故障排除

### 连接失败
- 检查服务器是否正在运行
- 确认防火墙设置
- 验证网络连接

### 编译错误
- 确保安装了正确的编译器
- 检查库文件链接
- 验证Windows SDK版本

### 运行时错误
- 检查服务器日志
- 验证用户权限
- 重启客户端程序

## 开发信息

- 开发语言：C++17
- 图形库：Windows API
- 网络库：Winsock2
- 编译器：MinGW-w64 / Visual Studio

## 版本历史

- v1.0 - 基础GUI功能
- v1.1 - 增强版界面和功能
- v1.2 - 改进错误处理和用户体验

## 联系支持

如遇到问题或需要技术支持，请查看服务器日志或联系系统管理员。
