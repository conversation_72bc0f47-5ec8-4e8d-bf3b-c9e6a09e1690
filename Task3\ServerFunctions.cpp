#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <memory>
#include <limits>
#include <map>
#include <ctime>
#include <mingw.thread.h>
#include <mingw.mutex.h>

using namespace std;

// 文件路径常量
const string USERS_FILE = "users.csv";
const string PRODUCTS_FILE = "products.csv";
extern std::mutex fileMutex;

// 用户基类（抽象类）
class User {
public:
    User(const string& username, const string& password, double balance)
        : username(username), password(password), balance(balance) {}

    virtual string getUserType() const = 0;

    void changePassword(const string& newPassword) {
        password = newPassword;
        saveUserInfo();
    }

    double queryBalance() const {
        return balance;
    }

    bool recharge(double amount) {
        if (amount > 0) {
            balance += amount;
            saveUserInfo();
            return true;
        }
        return false;
    }

    bool consume(double amount) {
        if (amount > 0 && amount <= balance) {
            balance -= amount;
            saveUserInfo();
            return true;
        }
        return false;
    }

    string getUserName() const {
        return username;
    }

    bool verifyPassword(const string& pwd) const {
        return password == pwd;
    }

protected:
    string username;
    string password;
    double balance;

    virtual void saveUserInfo() = 0;
};

// 消费者类
class Consumer : public User {
public:
    Consumer(const string& username, const string& password, double balance)
        : User(username, password, balance) {}

    string getUserType() const override {
        return "consumer";
    }

protected:
    void saveUserInfo() override;
};

// 商家类
class Merchant : public User {
public:
    Merchant(const string& username, const string& password, double balance)
        : User(username, password, balance) {}

    string getUserType() const override {
        return "merchant";
    }

    void addProduct(const string& name, const string& description, double price, int quantity, const string& type);
    bool manageProduct(const string& name, double* price, int* quantity);
    void receiveOrder(const string& orderId, const string& consumer, const string& date, double amount);
    void updateOrderStatus(const string& orderId, const string& newStatus);

protected:
    void saveUserInfo() override;
};

// 辅助函数：分割字符串
vector<string> split(const string& s, char delimiter) {
    vector<string> tokens;
    string token;
    istringstream tokenStream(s);
    while (getline(tokenStream, token, delimiter)) {
        tokens.push_back(token);
    }
    return tokens;
}

// 辅助函数：连接字符串
string join(const vector<string>& v, char delimiter) {
    string result;
    for (size_t i = 0; i < v.size(); ++i) {
        result += v[i];
        if (i < v.size() - 1) {
            result += delimiter;
        }
    }
    return result;
}

// Consumer类的saveUserInfo实现
void Consumer::saveUserInfo() {
    std::lock_guard<std::mutex> lock(fileMutex);
    vector<vector<string>> users;
    ifstream inFile(USERS_FILE);
    string line;

    // 读取CSV文件头
    if (getline(inFile, line)) {
        users.push_back(split(line, ','));
    }

    // 读取所有用户数据
    while (getline(inFile, line)) {
        vector<string> userData = split(line, ',');
        if (userData.size() >= 4 && userData[0] == username) {
            // 更新当前用户数据
            userData[1] = password;
            userData[2] = to_string(balance);
        }
        users.push_back(userData);
    }
    inFile.close();

    // 写回CSV文件
    ofstream outFile(USERS_FILE);
    for (const auto& user : users) {
        outFile << join(user, ',') << endl;
    }
    outFile.close();
}

// Merchant类的saveUserInfo实现
void Merchant::saveUserInfo() {
    std::lock_guard<std::mutex> lock(fileMutex);
    vector<vector<string>> users;
    ifstream inFile(USERS_FILE);
    string line;

    // 读取CSV文件头
    if (getline(inFile, line)) {
        users.push_back(split(line, ','));
    }

    // 读取所有用户数据
    while (getline(inFile, line)) {
        vector<string> userData = split(line, ',');
        if (userData.size() >= 4 && userData[0] == username) {
            // 更新当前用户数据
            userData[1] = password;
            userData[2] = to_string(balance);
        }
        users.push_back(userData);
    }
    inFile.close();

    // 写回CSV文件
    ofstream outFile(USERS_FILE);
    for (const auto& user : users) {
        outFile << join(user, ',') << endl;
    }
    outFile.close();
}

// Merchant类的addProduct实现
void Merchant::addProduct(const string& name, const string& description, double price, int quantity, const string& type) {
    std::lock_guard<std::mutex> lock(fileMutex);
    ofstream outFile(PRODUCTS_FILE, ios::app);
    outFile << name << "," << description << "," << price << "," << quantity << "," << type << "," << username << endl;
    outFile.close();
}

// Merchant类的manageProduct实现
bool Merchant::manageProduct(const string& name, double* price, int* quantity) {
    std::lock_guard<std::mutex> lock(fileMutex);
    vector<vector<string>> products;
    ifstream inFile(PRODUCTS_FILE);
    string line;
    bool found = false;

    // 读取CSV文件头
    if (getline(inFile, line)) {
        products.push_back(split(line, ','));
    }

    // 读取所有产品数据
    while (getline(inFile, line)) {
        vector<string> productData = split(line, ',');
        if (productData.size() >= 6 && productData[0] == name && productData[5] == username) {
            // 只有商品属于当前商家才能更新
            if (price != nullptr) {
                productData[2] = to_string(*price);
            }
            if (quantity != nullptr) {
                productData[3] = to_string(*quantity);
            }
            found = true;
        }
        products.push_back(productData);
    }
    inFile.close();

    if (found) {
        // 写回CSV文件
        ofstream outFile(PRODUCTS_FILE);
        for (const auto& product : products) {
            outFile << join(product, ',') << endl;
        }
        outFile.close();
    }
    return found;
}

// Merchant类的receiveOrder实现
void Merchant::receiveOrder(const string& orderId, const string& consumer, const string& date, double amount) {
    std::lock_guard<std::mutex> lock(fileMutex);
    string merchantOrdersFile = username + "_orders.csv";
    bool fileExists = ifstream(merchantOrdersFile).good();

    ofstream outFile(merchantOrdersFile, ios::app);
    if (!fileExists) {
        outFile << "orderId,consumer,date,amount,status" << endl;
    }

    outFile << orderId << "," << consumer << "," << date << "," << amount << ",unpaid" << endl;
    outFile.close();
}

// 新增方法：更新商家订单状态
void Merchant::updateOrderStatus(const string& orderId, const string& newStatus) {
    std::lock_guard<std::mutex> lock(fileMutex);
    string merchantOrdersFile = username + "_orders.csv";

    vector<vector<string>> orders;
    ifstream inFile(merchantOrdersFile);
    string line;

    if (!inFile.good()) {
        return; // 文件不存在，无需更新
    }

    // 读取CSV文件头
    if (getline(inFile, line)) {
        orders.push_back(split(line, ','));
    }

    // 读取所有订单数据
    while (getline(inFile, line)) {
        vector<string> orderData = split(line, ',');
        if (orderData.size() >= 5 && orderData[0] == orderId) {
            // 更新订单状态
            orderData[4] = newStatus;
        }
        orders.push_back(orderData);
    }
    inFile.close();

    // 写回CSV文件
    ofstream outFile(merchantOrdersFile);
    for (const auto& order : orders) {
        outFile << join(order, ',') << endl;
    }
    outFile.close();
}

// 加载用户信息
vector<shared_ptr<User>> loadUsers() {
    std::lock_guard<std::mutex> lock(fileMutex);
    vector<shared_ptr<User>> users;
    ifstream inFile(USERS_FILE);
    string line;

    // 跳过CSV文件头
    if (getline(inFile, line)) {
        // 文件头不处理
    }

    while (getline(inFile, line)) {
        vector<string> userData = split(line, ',');
        if (userData.size() >= 4) {
            string username = userData[0];
            string password = userData[1];
            double balance = stod(userData[2]);
            string type = userData[3];

            if (type == "consumer") {
                users.emplace_back(make_shared<Consumer>(username, password, balance));
            } else if (type == "merchant") {
                users.emplace_back(make_shared<Merchant>(username, password, balance));
            }
        }
    }
    inFile.close();
    return users;
}

// 检查密码复杂度
bool isPasswordComplex(const string& password) {
    if (password.length() < 6) {
        return false;
    }

    bool hasLetter = false;
    bool hasDigit = false;
    bool hasSymbol = false;

    for (char c : password) {
        if (isalpha(c)) {
            hasLetter = true;
        } else if (isdigit(c)) {
            hasDigit = true;
        } else {
            hasSymbol = true;
        }
    }

    return hasLetter && hasDigit && hasSymbol;
}

// 检查用户名是否已存在
bool isUsernameExists(const string& username) {
    auto users = loadUsers();
    for (const auto& user : users) {
        if (user->getUserName() == username) {
            return true;
        }
    }
    return false;
}

// 用户注册
shared_ptr<User> registerUser(const string& username, const string& password, const string& type) {
    // 检查用户名是否已存在
    if (isUsernameExists(username)) {
        cout << "This username already exists. Please choose another one." << endl;
        return nullptr;
    }

    // 检查密码复杂度
    if (!isPasswordComplex(password)) {
        cout << "Password must be at least 6 characters long and contain letters, digits, and symbols." << endl;
        return nullptr;
    }

    shared_ptr<User> newUser;
    if (type == "consumer") {
        newUser = make_shared<Consumer>(username, password, 0);
    } else if (type == "merchant") {
        newUser = make_shared<Merchant>(username, password, 0);
    } else {
        cout << "Invalid user type. Please enter 'consumer' or 'merchant'." << endl;
        return nullptr;
    }

    std::lock_guard<std::mutex> lock(fileMutex);
    // 检查文件是否存在，如果不存在则创建并添加表头
    ifstream checkFile(USERS_FILE);
    if (!checkFile.good()) {
        ofstream createFile(USERS_FILE);
        createFile << "username,password,balance,type" << endl;
        createFile.close();
    } else {
        checkFile.close();
    }

    ofstream outFile(USERS_FILE, ios::app);
    outFile << username << "," << password << "," << 0 << "," << type << endl;
    outFile.close();

    cout << "Registration successful!" << endl;
    return newUser;
}

// 用户登录
shared_ptr<User> login(const string& username, const string& password) {
    auto users = loadUsers();

    for (const auto& user : users) {
        if (user->getUserName() == username) {
            if (user->verifyPassword(password)) {
                return user;
            } else {
                return nullptr;
            }
        }
    }

    return nullptr;
}

// 展示平台商品信息
void showProducts(const string& filter) {
    std::lock_guard<std::mutex> lock(fileMutex);
    ifstream inFile(PRODUCTS_FILE);
    string line;

    // 跳过CSV文件头
    if (getline(inFile, line)) {
        // 文件头不处理
    }

    while (getline(inFile, line)) {
        vector<string> productData = split(line, ',');
        if (productData.size() >= 6) {  // 确保至少有6个字段
            string name = productData[0];
            string description = productData[1];
            double price = stod(productData[2]);
            int quantity = stoi(productData[3]);
            string type = productData[4];
            string merchant = productData[5];

            if (filter.empty() || name.find(filter) != string::npos) {
                cout << "Name: " << name << ", Description: " << description
                     << ", Price: " << price << ", Quantity: " << quantity
                     << ", Type: " << type << ", Merchant: " << merchant << endl;
            }
        }
    }
    inFile.close();
}

// 同一品类商品打折
void discountProducts(const string& type, double discountRate) {
    std::lock_guard<std::mutex> lock(fileMutex);
    vector<vector<string>> products;
    ifstream inFile(PRODUCTS_FILE);
    string line;

    // 读取CSV文件头
    if (getline(inFile, line)) {
        products.push_back(split(line, ','));
    }

    // 读取所有产品数据
    while (getline(inFile, line)) {
        vector<string> productData = split(line, ',');
        if (productData.size() >= 5 && productData[4] == type) {
            // 应用折扣
            double price = stod(productData[2]);
            price *= discountRate;
            productData[2] = to_string(price);
        }
        products.push_back(productData);
    }
    inFile.close();

    // 写回CSV文件
    ofstream outFile(PRODUCTS_FILE);
    for (const auto& product : products) {
        outFile << join(product, ',') << endl;
    }
    outFile.close();
}

// 初始化商品数据
void initProducts() {
    std::lock_guard<std::mutex> lock(fileMutex);
    ifstream checkFile(PRODUCTS_FILE);
    if (!checkFile.good()) {
        ofstream outFile(PRODUCTS_FILE);
        outFile << "name,description,price,quantity,type,merchant" << endl;
        outFile << "C++ Primer,A classic C++ learning book,100,20,Book,Xuan" << endl;
        outFile << "Effective C++,A book to improve C++ programming skills,80,15,Book,Xuan" << endl;
        outFile << "The C++ Programming Language,The authoritative C++ language book,120,10,Book,Xuan" << endl;
        outFile << "T-Shirt,A simple white T-shirt,50,30,Clothing,Xuan" << endl;
        outFile << "Jeans,Blue jeans,150,25,Clothing,Xuan" << endl;
        outFile << "Sweater,A gray sweater,120,20,Clothing,Xuan" << endl;
        outFile << "Chocolate,Rich chocolate,20,50,Food,Xuan" << endl;
        outFile << "Biscuit,Delicious biscuits,10,60,Food,Xuan" << endl;
        outFile << "Candy,Fruit hard candies,5,80,Food,Xuan" << endl;
        outFile << "Toothpaste,Mint flavor toothpaste,15,40,Personal Care,Xuan" << endl;
        outFile << "Shampoo,Moisturizing shampoo,25,35,Personal Care,Xuan" << endl;
        outFile << "Soap,Antibacterial soap,8,70,Personal Care,Xuan" << endl;
        outFile << "Toilet Paper,Soft toilet paper,12,100,Daily Necessities,Xuan" << endl;
        outFile << "Detergent,Powerful cleaning detergent,30,45,Daily Necessities,Xuan" << endl;
        outFile << "Refrigerator,Energy-efficient refrigerator,2000,5,Appliances,Xuan" << endl;
        outFile << "Microwave,Compact microwave oven,500,8,Appliances,Xuan" << endl;
        outFile << "Notebook,Lined notebook,5,200,Stationery and Sports Goods,Xuan" << endl;
        outFile << "Basketball,Standard size basketball,60,15,Stationery and Sports Goods,Xuan" << endl;
        outFile << "Teddy Bear,Soft plush teddy bear,40,25,Toys,Xuan" << endl;
        outFile << "Building Blocks,Educational building blocks,35,30,Toys,Xuan" << endl;
        outFile << "Cola,Refreshing cola drink,3,150,Beverages,Xuan" << endl;
        outFile << "Orange Juice,Fresh orange juice,4,120,Beverages,Xuan" << endl;
        outFile << "Baby Diapers,Absorbent baby diapers,50,40,Maternity and Baby Products,Xuan" << endl;
        outFile << "Baby Formula,Nutritious baby formula,80,30,Maternity and Baby Products,Xuan" << endl;
        outFile << "Gift Card,Redeemable gift card,100,50,Others,Xuan" << endl;
        outFile.close();
    }
    checkFile.close();
}
