@echo off
title Shopping Platform - Quick Start
echo ========================================
echo Shopping Platform Quick Start
echo ========================================
echo.

REM Check if server executable exists
if not exist "Server.exe" (
    echo Error: Server.exe not found!
    echo Please compile the server first using: g++ -std=c++17 -o Server.exe Server.cpp ServerFunctions.cpp -pthread
    echo.
    pause
    exit /b 1
)

REM Check if GUI client exists
if not exist "ClientGUI_Enhanced.exe" (
    echo GUI client not found. Attempting to compile...
    call compile_gui.bat
    if not exist "ClientGUI_Enhanced.exe" (
        echo Failed to compile GUI client!
        pause
        exit /b 1
    )
)

echo Starting server...
start "Shopping Platform Server" Server.exe

echo Waiting for server to initialize...
timeout /t 3 /nobreak >nul

echo Starting GUI client...
start "Shopping Platform GUI" ClientGUI_Enhanced.exe

echo.
echo ========================================
echo Both server and GUI client are starting!
echo ========================================
echo.
echo Instructions:
echo 1. The server window should open first
echo 2. The GUI client window will open shortly
echo 3. Use existing credentials to login:
echo    - Username: Orange_Ice (consumer)
echo    - Username: Xuan (merchant)
echo    - Or create new accounts using the console client
echo.
echo To stop the system:
echo - Close the GUI client window
echo - Close the server window
echo.
echo Press any key to exit this launcher...
pause >nul
