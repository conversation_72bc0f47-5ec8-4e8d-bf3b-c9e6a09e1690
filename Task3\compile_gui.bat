@echo off
echo Compiling GUI Clients...
echo.

echo Compiling Basic GUI Client...
g++ -std=c++17 -mwindows -o ClientGUI.exe ClientGUI.cpp -lws2_32 -lcomctl32
if %errorlevel% equ 0 (
    echo Basic GUI Client compilation successful! ClientGUI.exe created.
) else (
    echo Basic GUI Client compilation failed!
    echo Please check if MinGW-w64 is properly installed.
)

echo.
echo Compiling Enhanced GUI Client...
g++ -std=c++17 -mwindows -o ClientGUI_Enhanced.exe ClientGUI_Enhanced.cpp -lws2_32 -lcomctl32
if %errorlevel% equ 0 (
    echo Enhanced GUI Client compilation successful! ClientGUI_Enhanced.exe created.
    echo.
    echo ========================================
    echo GUI Clients Ready!
    echo ========================================
    echo.
    echo Available GUI clients:
    echo 1. ClientGUI.exe - Basic version
    echo 2. ClientGUI_Enhanced.exe - Enhanced version (Recommended)
    echo.
    echo To run the GUI client:
    echo 1. Make sure the server (Server.exe) is running
    echo 2. Run ClientGUI_Enhanced.exe for the best experience
    echo.
    echo Features in Enhanced version:
    echo - Real-time balance display
    echo - Complete order management
    echo - Account recharge functionality
    echo - Improved user interface
    echo - Better error handling
    echo - Product search functionality
    echo - Fixed add-to-cart issues
    echo - Enhanced input validation
) else (
    echo Enhanced GUI Client compilation failed!
)

echo.
pause
