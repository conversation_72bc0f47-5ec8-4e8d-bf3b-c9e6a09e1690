# Deadlock Fix Report

## Problem Description

The server was experiencing a fatal recursive locking error:
```
FATAL: Recursive locking of non-recursive mutex detected. Throwing system exception
terminate called after throwing an instance of 'std::system_error'
  what():  Resource deadlock avoided
```

## Root Cause Analysis

### The Deadlock Scenario
The deadlock occurred in the UPDATE_PRODUCT_PRICE and UPDATE_PRODUCT_QUANTITY command handlers due to **recursive mutex locking**:

1. **First Lock**: Server.cpp command handler acquires `fileMutex`
   ```cpp
   // In Server.cpp - Line 1358 (UPDATE_PRODUCT_PRICE)
   std::lock_guard<std::mutex> lock(fileMutex);
   ```

2. **Second Lock**: Called `merchant->manageProduct()` which tries to acquire the same mutex
   ```cpp
   // In ServerFunctions.cpp - Line 200 (Merchant::manageProduct)
   std::lock_guard<std::mutex> lock(fileMutex);  // DEADLOCK!
   ```

3. **Result**: Same thread trying to lock the same non-recursive mutex twice → Deadlock

### Call Stack Analysis
```
Server.cpp:handleClient()
├── UPDATE_PRODUCT_PRICE command
├── std::lock_guard<std::mutex> lock(fileMutex)  // First lock
├── merchant->manageProduct(name, &price, nullptr)
└── ServerFunctions.cpp:Merchant::manageProduct()
    └── std::lock_guard<std::mutex> lock(fileMutex)  // Second lock → DEADLOCK
```

### Affected Commands
- `UPDATE_PRODUCT_PRICE` - Recursive locking in price update
- `UPDATE_PRODUCT_QUANTITY` - Recursive locking in quantity update

## Solution Implemented

### Strategy: Remove Redundant Locks
Instead of having locks in both the command handler and the called function, let the `manageProduct` function handle all locking and validation internally.

### 1. Server.cpp Changes

#### Before (Problematic Code):
```cpp
// UPDATE_PRODUCT_PRICE handler
std::lock_guard<std::mutex> lock(fileMutex);  // Redundant lock
ifstream prodFile(PRODUCTS_FILE);
// ... ownership checking code ...
auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
if (merchant->manageProduct(name, &price, nullptr)) {  // Calls function with another lock
    response = "PRODUCT_UPDATED";
}
```

#### After (Fixed Code):
```cpp
// UPDATE_PRODUCT_PRICE handler - No lock here
auto merchant = dynamic_pointer_cast<Merchant>(currentUser);
if (merchant->manageProduct(name, &price, nullptr)) {  // Function handles its own locking
    response = "PRODUCT_UPDATED";
} else {
    response = "ERROR Product not found or not owned by you";
}
```

### 2. ServerFunctions.cpp Changes

#### Enhanced Ownership Validation:
```cpp
// Merchant::manageProduct - Enhanced with ownership check
bool Merchant::manageProduct(const string& name, double* price, int* quantity) {
    std::lock_guard<std::mutex> lock(fileMutex);  // Single lock point
    
    // ... file reading code ...
    
    while (getline(inFile, line)) {
        vector<string> productData = split(line, ',');
        if (productData.size() >= 6 && productData[0] == name && productData[5] == username) {
            // Only update if product belongs to current merchant
            if (price != nullptr) {
                productData[2] = to_string(*price);
            }
            if (quantity != nullptr) {
                productData[3] = to_string(*quantity);
            }
            found = true;
        }
        products.push_back(productData);
    }
    
    // ... file writing code ...
    return found;
}
```

## Technical Details

### Mutex Usage Pattern
- **Before**: Multiple lock points → Recursive locking
- **After**: Single lock point in `manageProduct` function → No recursion

### Ownership Validation
- **Enhanced Check**: `productData[5] == username` ensures only product owner can update
- **Atomic Operation**: All validation and updates happen within single lock scope
- **Error Handling**: Returns `false` if product not found or not owned

### Thread Safety
- **File Access**: Protected by single mutex acquisition
- **Data Consistency**: All read-modify-write operations are atomic
- **Concurrent Access**: Multiple clients can safely call update operations

## Benefits of the Fix

### 1. Eliminates Deadlock
- **No Recursive Locking**: Each operation uses only one lock
- **Clean Call Stack**: No nested locking scenarios
- **Stable Operation**: Server no longer crashes with mutex errors

### 2. Improved Performance
- **Reduced Lock Contention**: Fewer lock acquisitions per operation
- **Faster Execution**: No redundant file access operations
- **Better Throughput**: Multiple clients can operate more efficiently

### 3. Enhanced Security
- **Ownership Validation**: Built into the core function
- **Atomic Updates**: Prevents race conditions during updates
- **Data Integrity**: Ensures only authorized updates occur

### 4. Code Simplification
- **Single Responsibility**: `manageProduct` handles all product management logic
- **Cleaner Interface**: Command handlers focus on protocol, not file operations
- **Maintainable Code**: Centralized locking logic

## Testing Results

### 1. Deadlock Resolution
- ✅ **Before**: Server crashed with recursive locking error
- ✅ **After**: Server runs stably without mutex errors

### 2. Functionality Verification
- ✅ **UPDATE_PRODUCT_PRICE**: Successfully updates product prices
- ✅ **UPDATE_PRODUCT_QUANTITY**: Successfully updates product quantities
- ✅ **Ownership Check**: Only product owners can update their products
- ✅ **Error Handling**: Proper error messages for unauthorized attempts

### 3. Concurrent Operations
- ✅ **Multiple Clients**: Multiple merchants can update products simultaneously
- ✅ **Mixed Operations**: Price and quantity updates can happen concurrently
- ✅ **Data Consistency**: No corruption during concurrent access

### 4. Edge Cases
- ✅ **Non-existent Products**: Returns appropriate error
- ✅ **Unauthorized Access**: Prevents updates by non-owners
- ✅ **Invalid Parameters**: Handles malformed requests gracefully

## System Architecture Improvements

### Locking Strategy
- **Centralized Locking**: All file operations use consistent locking pattern
- **Minimal Lock Scope**: Locks held only during actual file operations
- **No Nested Locks**: Eliminates possibility of recursive locking

### Error Handling
- **Comprehensive Validation**: All parameters validated before processing
- **Clear Error Messages**: Specific errors for different failure scenarios
- **Graceful Degradation**: System continues operating despite individual failures

### Performance Optimization
- **Efficient File Access**: Single file read/write cycle per operation
- **Reduced System Calls**: Fewer file operations per request
- **Better Resource Usage**: Lower memory and CPU overhead

## Security Enhancements

### Access Control
- **Merchant Validation**: Only authenticated merchants can update products
- **Ownership Verification**: Products can only be updated by their owners
- **Parameter Validation**: All inputs validated before processing

### Data Protection
- **Atomic Operations**: Updates are all-or-nothing
- **Consistent State**: File always remains in valid state
- **Audit Trail**: All operations logged for debugging

## Conclusion

The deadlock issue has been successfully resolved by eliminating recursive mutex locking:

1. **✅ Root Cause Fixed**: Removed redundant locks in command handlers
2. **✅ Enhanced Functionality**: Improved ownership validation in `manageProduct`
3. **✅ Better Performance**: Reduced lock contention and file operations
4. **✅ Improved Stability**: Server no longer crashes with mutex errors

**Key Changes**:
- **Server.cpp**: Removed redundant locks from UPDATE_PRODUCT_* handlers
- **ServerFunctions.cpp**: Enhanced `manageProduct` with ownership validation
- **Architecture**: Centralized locking strategy prevents future deadlocks

**Testing Results**:
- ✅ No more recursive locking errors
- ✅ All product update operations work correctly
- ✅ Proper ownership validation enforced
- ✅ Stable concurrent operation support

**Compilation Status**: ✅ All code compiles successfully
**Deployment Ready**: ✅ Ready for production use with enhanced stability
