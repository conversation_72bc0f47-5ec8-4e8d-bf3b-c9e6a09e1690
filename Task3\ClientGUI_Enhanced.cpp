#define _WIN32_WINNT 0x0600
#define UNICODE
#define _UNICODE
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>
#include <sstream>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <cctype>
#include <cmath>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "comctl32.lib")

using namespace std;

// 窗口控件ID
#define ID_LOGIN_USERNAME 1001
#define ID_LOGIN_PASSWORD 1002
#define ID_LOGIN_BUTTON 1003
#define ID_REGISTER_BUTTON 1004
#define ID_EXIT_BUTTON 1005
#define ID_LOGIN_STATUS 1006

#define ID_MAIN_PRODUCTS 2001
#define ID_MAIN_CART 2002
#define ID_MAIN_ORDERS 2003
#define ID_MAIN_BALANCE 2004
#define ID_MAIN_RECHARGE 2005
#define ID_MAIN_LOGOUT 2006
#define ID_MAIN_STATUS 2007
#define ID_MAIN_BALANCE_DISPLAY 2008

#define ID_PRODUCTS_LIST 3001
#define ID_PRODUCTS_SEARCH 3002
#define ID_PRODUCTS_ADD_CART 3003
#define ID_PRODUCTS_BACK 3004
#define ID_PRODUCTS_REFRESH 3005
#define ID_PRODUCTS_QUANTITY 3006
#define ID_PRODUCTS_SEARCH_BUTTON 3007
#define ID_PRODUCTS_CLEAR_SEARCH 3008

#define ID_CART_LIST 4001
#define ID_CART_REMOVE 4002
#define ID_CART_GENERATE_ORDER 4003
#define ID_CART_BACK 4004
#define ID_CART_REFRESH 4005
#define ID_CART_TOTAL 4006

#define ID_ORDERS_LIST 5001
#define ID_ORDERS_PAY 5002
#define ID_ORDERS_DETAILS 5003
#define ID_ORDERS_BACK 5004
#define ID_ORDERS_REFRESH 5005

#define ID_RECHARGE_AMOUNT 6001
#define ID_RECHARGE_BUTTON 6002
#define ID_RECHARGE_BACK 6003
#define ID_RECHARGE_STATUS 6004

// 商家商品管理窗口控件ID
#define ID_MERCHANT_PRODUCTS_LIST 7001
#define ID_MERCHANT_PRODUCTS_ADD 7002
#define ID_MERCHANT_PRODUCTS_UPDATE_PRICE 7003
#define ID_MERCHANT_PRODUCTS_UPDATE_QUANTITY 7004
#define ID_MERCHANT_PRODUCTS_REFRESH 7005
#define ID_MERCHANT_PRODUCTS_BACK 7006

// 商家订单管理窗口控件ID
#define ID_MERCHANT_ORDERS_LIST 8001
#define ID_MERCHANT_ORDERS_DETAILS 8002
#define ID_MERCHANT_ORDERS_REFRESH 8003
#define ID_MERCHANT_ORDERS_BACK 8004

// 添加商品对话框控件ID
#define ID_ADD_PRODUCT_NAME 9001
#define ID_ADD_PRODUCT_DESC 9002
#define ID_ADD_PRODUCT_PRICE 9003
#define ID_ADD_PRODUCT_QUANTITY 9004
#define ID_ADD_PRODUCT_TYPE 9005
#define ID_ADD_PRODUCT_OK 9006
#define ID_ADD_PRODUCT_CANCEL 9007

// 折扣管理窗口控件ID
#define ID_DISCOUNT_LIST 10001
#define ID_DISCOUNT_CATEGORY 10002
#define ID_DISCOUNT_RATE 10003
#define ID_DISCOUNT_SET 10004
#define ID_DISCOUNT_REFRESH 10005
#define ID_DISCOUNT_BACK 10006

// 全局变量
HINSTANCE hInst;
HWND hMainWnd = NULL;
HWND hLoginWnd = NULL;
HWND hProductsWnd = NULL;
HWND hCartWnd = NULL;
HWND hOrdersWnd = NULL;
HWND hRechargeWnd = NULL;
HWND hMerchantProductsWnd = NULL;
HWND hMerchantOrdersWnd = NULL;
HWND hDiscountWnd = NULL;

SOCKET clientSocket = INVALID_SOCKET;
string currentUsername;
string userType;
double currentBalance = 0.0;
vector<vector<string>> allProducts;        // 所有商品
vector<vector<string>> currentProducts;    // 当前显示的商品（可能是搜索结果）
vector<vector<string>> currentCart;
vector<vector<string>> currentOrders;
vector<vector<string>> merchantProducts;   // 商家自己的商品
vector<vector<string>> merchantOrders;     // 商家收到的订单
vector<vector<string>> currentDiscounts;   // 当前折扣信息

// 服务器配置
const string SERVER_IP = "127.0.0.1";
const int PORT = 8888;
const int BUFFER_SIZE = 4096;

// 函数声明
LRESULT CALLBACK LoginWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK MainWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK ProductsWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK CartWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK OrdersWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK RechargeWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK MerchantProductsWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK MerchantOrdersWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK AddProductWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK DiscountWndProc(HWND, UINT, WPARAM, LPARAM);

bool InitializeWinsock();
bool ConnectToServer();
string SendRequest(const string& request);
vector<string> Split(const string& s, char delimiter);
void ShowLoginWindow();
void ShowMainWindow();
void ShowProductsWindow();
void ShowCartWindow();
void ShowOrdersWindow();
void ShowRechargeWindow();
void ShowMerchantProductsWindow();
void ShowMerchantOrdersWindow();
void ShowAddProductDialog();
void ShowDiscountWindow();
void LoadProducts();
void LoadCart();
void LoadOrders();
void LoadMerchantProducts();
void LoadMerchantOrders();
void LoadDiscounts();
void UpdateProductsList();
void UpdateCartList();
void UpdateOrdersList();
void UpdateMerchantProductsList();
void UpdateMerchantOrdersList();
void UpdateDiscountsList();
void UpdateBalance();
void UpdateMainWindowInfo();
void SearchProducts(const string& searchTerm);
void ResetProductsFilter();
string FormatCurrency(double amount);
wstring StringToWString(const string& str);
bool InputBox(HWND hParent, const wchar_t* prompt, const wchar_t* title, wchar_t* result, int maxLength);

// 全局变量用于InputBox
static wchar_t* g_inputResult = nullptr;
static int g_inputMaxLength = 0;

// 初始化Winsock
bool InitializeWinsock() {
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        MessageBox(NULL, L"WSAStartup failed", L"Error", MB_OK | MB_ICONERROR);
        return false;
    }
    return true;
}

// 连接到服务器
bool ConnectToServer() {
    clientSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (clientSocket == INVALID_SOCKET) {
        MessageBox(NULL, L"Socket creation failed", L"Error", MB_OK | MB_ICONERROR);
        return false;
    }

    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(PORT);
    inet_pton(AF_INET, SERVER_IP.c_str(), &serverAddr.sin_addr);

    if (connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        MessageBox(NULL, L"Connection to server failed. Please make sure the server is running.", L"Connection Error", MB_OK | MB_ICONERROR);
        closesocket(clientSocket);
        clientSocket = INVALID_SOCKET;
        return false;
    }

    return true;
}

// 发送请求到服务器
string SendRequest(const string& request) {
    if (clientSocket == INVALID_SOCKET) {
        return "ERROR_NOT_CONNECTED";
    }

    int result = send(clientSocket, request.c_str(), request.length(), 0);
    if (result == SOCKET_ERROR) {
        return "ERROR_SEND_FAILED";
    }

    char buffer[BUFFER_SIZE];
    memset(buffer, 0, BUFFER_SIZE);
    result = recv(clientSocket, buffer, BUFFER_SIZE, 0);

    if (result <= 0) {
        return "ERROR_RECEIVE_FAILED";
    }

    return string(buffer);
}

// 分割字符串
vector<string> Split(const string& s, char delimiter) {
    vector<string> tokens;
    string token;
    istringstream tokenStream(s);
    while (getline(tokenStream, token, delimiter)) {
        tokens.push_back(token);
    }
    return tokens;
}

// 格式化货币显示
string FormatCurrency(double amount) {
    ostringstream oss;
    oss << fixed << setprecision(2) << "$" << amount;
    return oss.str();
}

// 字符串转宽字符串
wstring StringToWString(const string& str) {
    if (str.empty()) return wstring();
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

// 更新余额
void UpdateBalance() {
    string response = SendRequest("GET_BALANCE");
    if (response.find("BALANCE") == 0) {
        string balanceStr = response.substr(8);
        try {
            currentBalance = stod(balanceStr);
        } catch (...) {
            currentBalance = 0.0;
        }
    }
}

// 更新主窗口信息
void UpdateMainWindowInfo() {
    if (hMainWnd) {
        UpdateBalance();

        // 更新用户信息显示
        string userInfo = "Welcome, " + currentUsername + " (" + userType + ")";
        SetWindowText(GetDlgItem(hMainWnd, ID_MAIN_STATUS), StringToWString(userInfo).c_str());

        // 更新余额显示
        string balanceInfo = "Current Balance: " + FormatCurrency(currentBalance);
        SetWindowText(GetDlgItem(hMainWnd, ID_MAIN_BALANCE_DISPLAY), StringToWString(balanceInfo).c_str());
    }
}

// 登录窗口过程
LRESULT CALLBACK LoginWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Shopping Platform Login",
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 350, 30, hWnd, NULL, hInst, NULL);

            // 创建用户名标签和输入框
            CreateWindow(L"STATIC", L"Username:", WS_VISIBLE | WS_CHILD,
                50, 70, 100, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
                160, 70, 200, 25, hWnd, (HMENU)ID_LOGIN_USERNAME, hInst, NULL);

            // 创建密码标签和输入框
            CreateWindow(L"STATIC", L"Password:", WS_VISIBLE | WS_CHILD,
                50, 110, 100, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | ES_PASSWORD,
                160, 110, 200, 25, hWnd, (HMENU)ID_LOGIN_PASSWORD, hInst, NULL);

            // 创建状态显示
            CreateWindow(L"STATIC", L"Please enter your credentials",
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 150, 350, 20, hWnd, (HMENU)ID_LOGIN_STATUS, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Login", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 180, 80, 35, hWnd, (HMENU)ID_LOGIN_BUTTON, hInst, NULL);
            CreateWindow(L"BUTTON", L"Register", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                140, 180, 80, 35, hWnd, (HMENU)ID_REGISTER_BUTTON, hInst, NULL);
            CreateWindow(L"BUTTON", L"Exit", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                280, 180, 80, 35, hWnd, (HMENU)ID_EXIT_BUTTON, hInst, NULL);
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_LOGIN_BUTTON:
            {
                SetWindowText(GetDlgItem(hWnd, ID_LOGIN_STATUS), L"Logging in...");

                // 获取用户名和密码
                wchar_t username[256], password[256];
                GetWindowText(GetDlgItem(hWnd, ID_LOGIN_USERNAME), username, 256);
                GetWindowText(GetDlgItem(hWnd, ID_LOGIN_PASSWORD), password, 256);

                // 转换为string
                char usernameA[256], passwordA[256];
                WideCharToMultiByte(CP_UTF8, 0, username, -1, usernameA, 256, NULL, NULL);
                WideCharToMultiByte(CP_UTF8, 0, password, -1, passwordA, 256, NULL, NULL);

                // 验证输入
                if (strlen(usernameA) == 0 || strlen(passwordA) == 0) {
                    SetWindowText(GetDlgItem(hWnd, ID_LOGIN_STATUS), L"Please enter both username and password");
                    break;
                }

                // 发送登录请求
                string loginRequest = "LOGIN " + string(usernameA) + " " + string(passwordA);
                string response = SendRequest(loginRequest);

                if (response.find("LOGIN_SUCCESS") == 0) {
                    // 解析用户类型
                    vector<string> parts = Split(response, ' ');
                    if (parts.size() >= 2) {
                        currentUsername = usernameA;
                        userType = parts[1];
                        SetWindowText(GetDlgItem(hWnd, ID_LOGIN_STATUS), L"Login successful!");
                        ShowWindow(hWnd, SW_HIDE);
                        ShowMainWindow();
                    }
                } else {
                    SetWindowText(GetDlgItem(hWnd, ID_LOGIN_STATUS), L"Login failed. Please check your credentials.");
                }
            }
            break;

        case ID_REGISTER_BUTTON:
            SetWindowText(GetDlgItem(hWnd, ID_LOGIN_STATUS), L"Registration feature available in console version");
            break;

        case ID_EXIT_BUTTON:
            PostQuitMessage(0);
            break;
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 主窗口过程
LRESULT CALLBACK MainWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建欢迎标签
            CreateWindow(L"STATIC", L"Shopping Platform",
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 500, 30, hWnd, NULL, hInst, NULL);

            // 创建用户状态显示
            CreateWindow(L"STATIC", L"",
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 50, 500, 25, hWnd, (HMENU)ID_MAIN_STATUS, hInst, NULL);

            // 创建余额显示
            CreateWindow(L"STATIC", L"",
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 75, 500, 25, hWnd, (HMENU)ID_MAIN_BALANCE_DISPLAY, hInst, NULL);

            // 根据用户类型创建不同的功能按钮
            if (userType == "merchant") {
                // 商家功能按钮 - 第一行
                CreateWindow(L"BUTTON", L"Manage Products", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    50, 120, 150, 40, hWnd, (HMENU)ID_MAIN_PRODUCTS, hInst, NULL);
                CreateWindow(L"BUTTON", L"Received Orders", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    220, 120, 150, 40, hWnd, (HMENU)ID_MAIN_CART, hInst, NULL);
                CreateWindow(L"BUTTON", L"Check Balance", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    390, 120, 150, 40, hWnd, (HMENU)ID_MAIN_BALANCE, hInst, NULL);

                // 商家功能按钮 - 第二行
                CreateWindow(L"BUTTON", L"Manage Discounts", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    50, 180, 150, 40, hWnd, (HMENU)ID_MAIN_ORDERS, hInst, NULL);
                CreateWindow(L"BUTTON", L"Recharge", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    220, 180, 150, 40, hWnd, (HMENU)ID_MAIN_RECHARGE, hInst, NULL);
                CreateWindow(L"BUTTON", L"Logout", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    390, 180, 150, 40, hWnd, (HMENU)ID_MAIN_LOGOUT, hInst, NULL);
            } else {
                // 消费者功能按钮 - 第一行
                CreateWindow(L"BUTTON", L"Browse Products", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    50, 120, 150, 40, hWnd, (HMENU)ID_MAIN_PRODUCTS, hInst, NULL);
                CreateWindow(L"BUTTON", L"Shopping Cart", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    220, 120, 150, 40, hWnd, (HMENU)ID_MAIN_CART, hInst, NULL);
                CreateWindow(L"BUTTON", L"My Orders", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    390, 120, 150, 40, hWnd, (HMENU)ID_MAIN_ORDERS, hInst, NULL);

                // 消费者功能按钮 - 第二行
                CreateWindow(L"BUTTON", L"Check Balance", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    50, 180, 150, 40, hWnd, (HMENU)ID_MAIN_BALANCE, hInst, NULL);
                CreateWindow(L"BUTTON", L"Recharge", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    220, 180, 150, 40, hWnd, (HMENU)ID_MAIN_RECHARGE, hInst, NULL);
                CreateWindow(L"BUTTON", L"Logout", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                    390, 180, 150, 40, hWnd, (HMENU)ID_MAIN_LOGOUT, hInst, NULL);
            }

            // 更新窗口信息
            UpdateMainWindowInfo();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_MAIN_PRODUCTS:
            if (userType == "merchant") {
                ShowMerchantProductsWindow();
            } else {
                ShowProductsWindow();
            }
            break;
        case ID_MAIN_CART:
            if (userType == "merchant") {
                ShowMerchantOrdersWindow();
            } else {
                ShowCartWindow();
            }
            break;
        case ID_MAIN_ORDERS:
            if (userType == "merchant") {
                ShowDiscountWindow();
            } else {
                ShowOrdersWindow();
            }
            break;
        case ID_MAIN_BALANCE:
            {
                UpdateBalance();
                string balanceMsg = "Current Balance: " + FormatCurrency(currentBalance);
                MessageBox(hWnd, StringToWString(balanceMsg).c_str(), L"Account Balance", MB_OK | MB_ICONINFORMATION);
                UpdateMainWindowInfo();
            }
            break;
        case ID_MAIN_RECHARGE:
            ShowRechargeWindow();
            break;
        case ID_MAIN_LOGOUT:
            {
                int result = MessageBox(hWnd, L"Are you sure you want to logout?", L"Confirm Logout", MB_YESNO | MB_ICONQUESTION);
                if (result == IDYES) {
                    SendRequest("LOGOUT");

                    // 清理用户数据
                    currentUsername = "";
                    userType = "";
                    currentBalance = 0.0;

                    // 清理所有数据容器
                    allProducts.clear();
                    currentProducts.clear();
                    currentCart.clear();
                    currentOrders.clear();
                    merchantProducts.clear();
                    merchantOrders.clear();
                    currentDiscounts.clear();

                    // 销毁所有子窗口
                    if (hProductsWnd) {
                        DestroyWindow(hProductsWnd);
                        hProductsWnd = NULL;
                    }
                    if (hCartWnd) {
                        DestroyWindow(hCartWnd);
                        hCartWnd = NULL;
                    }
                    if (hOrdersWnd) {
                        DestroyWindow(hOrdersWnd);
                        hOrdersWnd = NULL;
                    }
                    if (hRechargeWnd) {
                        DestroyWindow(hRechargeWnd);
                        hRechargeWnd = NULL;
                    }
                    if (hMerchantProductsWnd) {
                        DestroyWindow(hMerchantProductsWnd);
                        hMerchantProductsWnd = NULL;
                    }
                    if (hMerchantOrdersWnd) {
                        DestroyWindow(hMerchantOrdersWnd);
                        hMerchantOrdersWnd = NULL;
                    }
                    if (hDiscountWnd) {
                        DestroyWindow(hDiscountWnd);
                        hDiscountWnd = NULL;
                    }

                    ShowWindow(hWnd, SW_HIDE);
                    ShowLoginWindow();
                }
            }
            break;
        }
        break;

    case WM_ACTIVATE:
        if (LOWORD(wParam) == WA_ACTIVE || LOWORD(wParam) == WA_CLICKACTIVE) {
            UpdateMainWindowInfo();
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 商品浏览窗口过程
LRESULT CALLBACK ProductsWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Browse Products", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 600, 30, hWnd, NULL, hInst, NULL);

            // 创建商品列表框
            CreateWindow(L"LISTBOX", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | LBS_NOTIFY,
                50, 60, 600, 300, hWnd, (HMENU)ID_PRODUCTS_LIST, hInst, NULL);

            // 创建搜索框
            CreateWindow(L"STATIC", L"Search:", WS_VISIBLE | WS_CHILD,
                50, 380, 60, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
                120, 380, 150, 25, hWnd, (HMENU)ID_PRODUCTS_SEARCH, hInst, NULL);
            CreateWindow(L"BUTTON", L"Search", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                280, 380, 60, 25, hWnd, (HMENU)ID_PRODUCTS_SEARCH_BUTTON, hInst, NULL);

            // 创建数量输入框
            CreateWindow(L"STATIC", L"Quantity:", WS_VISIBLE | WS_CHILD,
                360, 380, 60, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"1", WS_VISIBLE | WS_CHILD | WS_BORDER | ES_NUMBER,
                430, 380, 50, 25, hWnd, (HMENU)ID_PRODUCTS_QUANTITY, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Add to Cart", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 420, 100, 35, hWnd, (HMENU)ID_PRODUCTS_ADD_CART, hInst, NULL);
            CreateWindow(L"BUTTON", L"Refresh", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                160, 420, 100, 35, hWnd, (HMENU)ID_PRODUCTS_REFRESH, hInst, NULL);
            CreateWindow(L"BUTTON", L"Clear Search", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                270, 420, 100, 35, hWnd, (HMENU)ID_PRODUCTS_CLEAR_SEARCH, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                550, 420, 100, 35, hWnd, (HMENU)ID_PRODUCTS_BACK, hInst, NULL);

            // 加载商品数据
            LoadProducts();
            UpdateProductsList();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_PRODUCTS_SEARCH_BUTTON:
            {
                wchar_t searchText[256];
                GetWindowText(GetDlgItem(hWnd, ID_PRODUCTS_SEARCH), searchText, 256);

                char searchTextA[256];
                WideCharToMultiByte(CP_UTF8, 0, searchText, -1, searchTextA, 256, NULL, NULL);

                SearchProducts(string(searchTextA));
                UpdateProductsList();
            }
            break;
        case ID_PRODUCTS_CLEAR_SEARCH:
            {
                SetWindowText(GetDlgItem(hWnd, ID_PRODUCTS_SEARCH), L"");
                ResetProductsFilter();
                UpdateProductsList();
            }
            break;
        case ID_PRODUCTS_ADD_CART:
            {
                try {
                    HWND hList = GetDlgItem(hWnd, ID_PRODUCTS_LIST);
                    int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                    if (selection == LB_ERR) {
                        MessageBox(hWnd, L"Please select a product first", L"No Selection", MB_OK | MB_ICONWARNING);
                        break;
                    }

                    if (selection >= currentProducts.size()) {
                        MessageBox(hWnd, L"Invalid product selection", L"Error", MB_OK | MB_ICONERROR);
                        break;
                    }

                    // 获取数量
                    wchar_t quantityText[10];
                    GetWindowText(GetDlgItem(hWnd, ID_PRODUCTS_QUANTITY), quantityText, 10);
                    int quantity = _wtoi(quantityText);

                    if (quantity <= 0) {
                        MessageBox(hWnd, L"Please enter a valid quantity (greater than 0)", L"Invalid Quantity", MB_OK | MB_ICONWARNING);
                        break;
                    }

                    string productName = currentProducts[selection][0];
                    string addRequest = "ADD_TO_CART " + productName + " " + to_string(quantity);

                    // 显示正在处理的消息
                    SetWindowText(GetDlgItem(hWnd, ID_PRODUCTS_ADD_CART), L"Adding...");
                    UpdateWindow(hWnd);

                    string response = SendRequest(addRequest);

                    // 恢复按钮文本
                    SetWindowText(GetDlgItem(hWnd, ID_PRODUCTS_ADD_CART), L"Add to Cart");

                    if (response == "CART_UPDATED") {
                        string successMsg = "Added " + to_string(quantity) + " x " + productName + " to cart!";
                        MessageBox(hWnd, StringToWString(successMsg).c_str(), L"Success", MB_OK | MB_ICONINFORMATION);

                        // 重置数量为1
                        SetWindowText(GetDlgItem(hWnd, ID_PRODUCTS_QUANTITY), L"1");
                    } else {
                        string errorMsg = "Failed to add item to cart. Server response: " + response;
                        MessageBox(hWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
                    }
                } catch (const exception& e) {
                    SetWindowText(GetDlgItem(hWnd, ID_PRODUCTS_ADD_CART), L"Add to Cart");
                    string errorMsg = "Error adding to cart: " + string(e.what());
                    MessageBox(hWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
                }
            }
            break;
        case ID_PRODUCTS_REFRESH:
            LoadProducts();
            ResetProductsFilter();
            UpdateProductsList();
            break;
        case ID_PRODUCTS_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_ACTIVATE:
        if (LOWORD(wParam) == WA_ACTIVE || LOWORD(wParam) == WA_CLICKACTIVE) {
            // 窗口激活时自动刷新商品列表
            LoadProducts();
            UpdateProductsList();
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 购物车窗口过程
LRESULT CALLBACK CartWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Shopping Cart", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 600, 30, hWnd, NULL, hInst, NULL);

            // 创建购物车列表框
            CreateWindow(L"LISTBOX", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | LBS_NOTIFY,
                50, 60, 600, 300, hWnd, (HMENU)ID_CART_LIST, hInst, NULL);

            // 创建总价显示
            CreateWindow(L"STATIC", L"Total: $0.00", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 370, 600, 25, hWnd, (HMENU)ID_CART_TOTAL, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Remove Item", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 410, 120, 35, hWnd, (HMENU)ID_CART_REMOVE, hInst, NULL);
            CreateWindow(L"BUTTON", L"Generate Order", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                180, 410, 120, 35, hWnd, (HMENU)ID_CART_GENERATE_ORDER, hInst, NULL);
            CreateWindow(L"BUTTON", L"Refresh", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                310, 410, 120, 35, hWnd, (HMENU)ID_CART_REFRESH, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                530, 410, 120, 35, hWnd, (HMENU)ID_CART_BACK, hInst, NULL);

            // 加载购物车数据
            LoadCart();
            UpdateCartList();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_CART_REMOVE:
            {
                HWND hList = GetDlgItem(hWnd, ID_CART_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                if (selection != LB_ERR) {
                    string removeRequest = "REMOVE_FROM_CART " + to_string(selection + 1);
                    string response = SendRequest(removeRequest);

                    if (response == "CART_UPDATED") {
                        LoadCart();
                        UpdateCartList();
                        MessageBox(hWnd, L"Item removed from cart!", L"Success", MB_OK | MB_ICONINFORMATION);
                    } else {
                        MessageBox(hWnd, L"Failed to remove item", L"Error", MB_OK | MB_ICONERROR);
                    }
                } else {
                    MessageBox(hWnd, L"Please select an item to remove", L"No Selection", MB_OK | MB_ICONWARNING);
                }
            }
            break;
        case ID_CART_GENERATE_ORDER:
            {
                if (currentCart.empty()) {
                    MessageBox(hWnd, L"Your cart is empty. Please add items before generating an order.", L"Empty Cart", MB_OK | MB_ICONWARNING);
                    break;
                }

                // 获取当前选中的商品
                HWND hList = GetDlgItem(hWnd, ID_CART_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);

                if (selection == LB_ERR) {
                    // 如果没有选中商品，询问是否生成所有商品的订单
                    int result = MessageBox(hWnd, L"No item selected. Generate order for all items in cart?", L"Confirm Order", MB_YESNO | MB_ICONQUESTION);
                    if (result == IDYES) {
                        string generateRequest = "GENERATE_ORDER all";
                        string response = SendRequest(generateRequest);

                        if (response.find("ORDER_GENERATED") == 0) {
                            MessageBox(hWnd, L"Order generated successfully! You can view and pay for it in 'My Orders'.", L"Success", MB_OK | MB_ICONINFORMATION);
                            LoadCart();
                            UpdateCartList();
                        } else {
                            MessageBox(hWnd, L"Failed to generate order. Please try again.", L"Error", MB_OK | MB_ICONERROR);
                        }
                    }
                } else {
                    // 为选中的商品生成订单
                    if (selection < currentCart.size()) {
                        string itemName = currentCart[selection][0];
                        wstring confirmMsg = L"Generate order for selected item: " + StringToWString(itemName) + L"?";
                        int result = MessageBox(hWnd, confirmMsg.c_str(), L"Confirm Order", MB_YESNO | MB_ICONQUESTION);

                        if (result == IDYES) {
                            // 发送选中商品的索引（从1开始）
                            string generateRequest = "GENERATE_ORDER " + to_string(selection + 1);
                            string response = SendRequest(generateRequest);

                            if (response.find("ORDER_GENERATED") == 0) {
                                MessageBox(hWnd, L"Order generated successfully! You can view and pay for it in 'My Orders'.", L"Success", MB_OK | MB_ICONINFORMATION);
                                LoadCart();
                                UpdateCartList();
                            } else {
                                MessageBox(hWnd, L"Failed to generate order. Please try again.", L"Error", MB_OK | MB_ICONERROR);
                            }
                        }
                    }
                }
            }
            break;
        case ID_CART_REFRESH:
            LoadCart();
            UpdateCartList();
            break;
        case ID_CART_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_ACTIVATE:
        if (LOWORD(wParam) == WA_ACTIVE || LOWORD(wParam) == WA_CLICKACTIVE) {
            // 窗口激活时自动刷新购物车列表
            LoadCart();
            UpdateCartList();
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 订单窗口过程
LRESULT CALLBACK OrdersWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"My Orders", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 600, 30, hWnd, NULL, hInst, NULL);

            // 创建订单列表框
            CreateWindow(L"LISTBOX", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | LBS_NOTIFY,
                50, 60, 600, 300, hWnd, (HMENU)ID_ORDERS_LIST, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Pay Order", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 380, 120, 35, hWnd, (HMENU)ID_ORDERS_PAY, hInst, NULL);
            CreateWindow(L"BUTTON", L"View Details", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                180, 380, 120, 35, hWnd, (HMENU)ID_ORDERS_DETAILS, hInst, NULL);
            CreateWindow(L"BUTTON", L"Refresh", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                310, 380, 120, 35, hWnd, (HMENU)ID_ORDERS_REFRESH, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                530, 380, 120, 35, hWnd, (HMENU)ID_ORDERS_BACK, hInst, NULL);

            // 加载订单数据
            LoadOrders();
            UpdateOrdersList();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_ORDERS_PAY:
            {
                HWND hList = GetDlgItem(hWnd, ID_ORDERS_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                if (selection != LB_ERR && selection < currentOrders.size()) {
                    string orderId = currentOrders[selection][0];
                    string status = currentOrders[selection][3];

                    if (status != "unpaid") {
                        MessageBox(hWnd, L"This order is already paid or processed.", L"Cannot Pay", MB_OK | MB_ICONWARNING);
                        break;
                    }

                    // 弹出密码输入对话框
                    wchar_t password[256] = L"";
                    bool passwordEntered = InputBox(hWnd, L"Enter your password to confirm payment:", L"Payment Confirmation", password, 256);

                    if (passwordEntered) {
                        // 转换密码为string
                        char passwordA[256];
                        WideCharToMultiByte(CP_UTF8, 0, password, -1, passwordA, 256, NULL, NULL);

                        if (strlen(passwordA) == 0) {
                            MessageBox(hWnd, L"Password cannot be empty.", L"Invalid Input", MB_OK | MB_ICONWARNING);
                            break;
                        }

                        // 显示处理中的消息
                        SetWindowText(GetDlgItem(hWnd, ID_ORDERS_PAY), L"Processing...");
                        UpdateWindow(hWnd);

                        string payRequest = "PAY_ORDER " + orderId + " " + string(passwordA);
                        string response = SendRequest(payRequest);

                        // 恢复按钮文本
                        SetWindowText(GetDlgItem(hWnd, ID_ORDERS_PAY), L"Pay Order");

                        if (response == "PAYMENT_SUCCESS") {
                            MessageBox(hWnd, L"Payment successful!", L"Success", MB_OK | MB_ICONINFORMATION);
                            LoadOrders();
                            UpdateOrdersList();
                            UpdateMainWindowInfo(); // 更新余额显示
                        } else if (response.empty()) {
                            MessageBox(hWnd, L"No response from server. Please check your connection and try again.", L"Connection Error", MB_OK | MB_ICONERROR);
                        } else {
                            wstring errorMsg = L"Payment failed: " + StringToWString(response);
                            MessageBox(hWnd, errorMsg.c_str(), L"Payment Failed", MB_OK | MB_ICONERROR);
                        }
                    } else {
                        // 用户取消了密码输入
                        MessageBox(hWnd, L"Payment cancelled.", L"Cancelled", MB_OK | MB_ICONINFORMATION);
                    }
                } else {
                    MessageBox(hWnd, L"Please select an order to pay", L"No Selection", MB_OK | MB_ICONWARNING);
                }
            }
            break;
        case ID_ORDERS_DETAILS:
            {
                HWND hList = GetDlgItem(hWnd, ID_ORDERS_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                if (selection != LB_ERR && selection < currentOrders.size()) {
                    string orderId = currentOrders[selection][0];
                    string detailsRequest = "GET_ORDER_DETAILS " + orderId;
                    string response = SendRequest(detailsRequest);

                    if (response.find("ORDER_DETAILS") == 0) {
                        string details = "Order ID: " + orderId + "\n";
                        details += "Date: " + currentOrders[selection][2] + "\n";
                        details += "Status: " + currentOrders[selection][3] + "\n";
                        details += "Total: $" + currentOrders[selection][4];

                        MessageBox(hWnd, StringToWString(details).c_str(), L"Order Details", MB_OK | MB_ICONINFORMATION);
                    } else {
                        MessageBox(hWnd, L"Failed to retrieve order details", L"Error", MB_OK | MB_ICONERROR);
                    }
                } else {
                    MessageBox(hWnd, L"Please select an order to view details", L"No Selection", MB_OK | MB_ICONWARNING);
                }
            }
            break;
        case ID_ORDERS_REFRESH:
            LoadOrders();
            UpdateOrdersList();
            break;
        case ID_ORDERS_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_ACTIVATE:
        if (LOWORD(wParam) == WA_ACTIVE || LOWORD(wParam) == WA_CLICKACTIVE) {
            // 窗口激活时自动刷新订单列表
            LoadOrders();
            UpdateOrdersList();
            UpdateMainWindowInfo(); // 更新主窗口的余额显示
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 充值窗口过程
LRESULT CALLBACK RechargeWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Recharge Account", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 400, 30, hWnd, NULL, hInst, NULL);

            // 显示当前余额
            string balanceText = "Current Balance: " + FormatCurrency(currentBalance);
            CreateWindow(L"STATIC", StringToWString(balanceText).c_str(), WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 60, 400, 25, hWnd, (HMENU)ID_RECHARGE_STATUS, hInst, NULL);

            // 创建充值金额输入
            CreateWindow(L"STATIC", L"Recharge Amount:", WS_VISIBLE | WS_CHILD,
                50, 100, 120, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | ES_NUMBER,
                180, 100, 150, 25, hWnd, (HMENU)ID_RECHARGE_AMOUNT, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Recharge", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 150, 100, 35, hWnd, (HMENU)ID_RECHARGE_BUTTON, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                280, 150, 100, 35, hWnd, (HMENU)ID_RECHARGE_BACK, hInst, NULL);
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_RECHARGE_BUTTON:
            {
                wchar_t amountText[20];
                GetWindowText(GetDlgItem(hWnd, ID_RECHARGE_AMOUNT), amountText, 20);

                if (wcslen(amountText) == 0) {
                    MessageBox(hWnd, L"Please enter a recharge amount", L"Invalid Input", MB_OK | MB_ICONWARNING);
                    break;
                }

                double amount = _wtof(amountText);
                if (amount <= 0) {
                    MessageBox(hWnd, L"Please enter a valid amount (greater than 0)", L"Invalid Amount", MB_OK | MB_ICONWARNING);
                    break;
                }

                string rechargeRequest = "RECHARGE " + to_string(amount);
                string response = SendRequest(rechargeRequest);

                if (response.find("RECHARGE_SUCCESS") == 0) {
                    string successMsg = "Recharge successful! Added " + FormatCurrency(amount) + " to your account.";
                    MessageBox(hWnd, StringToWString(successMsg).c_str(), L"Success", MB_OK | MB_ICONINFORMATION);

                    // 更新余额显示
                    UpdateBalance();
                    string balanceText = "Current Balance: " + FormatCurrency(currentBalance);
                    SetWindowText(GetDlgItem(hWnd, ID_RECHARGE_STATUS), StringToWString(balanceText).c_str());

                    // 清空输入框
                    SetWindowText(GetDlgItem(hWnd, ID_RECHARGE_AMOUNT), L"");
                } else {
                    MessageBox(hWnd, L"Recharge failed. Please try again.", L"Error", MB_OK | MB_ICONERROR);
                }
            }
            break;
        case ID_RECHARGE_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 加载商品数据
void LoadProducts() {
    allProducts.clear();
    currentProducts.clear();

    try {
        string response = SendRequest("GET_PRODUCTS");

        // 调试：检查响应
        if (response.empty()) {
            MessageBox(NULL, L"Empty response from server", L"Debug", MB_OK);
            return;
        }

        if (response.find("ERROR") == 0) {
            MessageBox(NULL, L"Server returned error", L"Debug", MB_OK);
            return;
        }

        if (response.find("PRODUCTS") == 0) {
            string productsData = response.substr(9); // 跳过"PRODUCTS "
            istringstream productsStream(productsData);
            string line;

            // 跳过CSV文件头
            if (getline(productsStream, line)) {
                // 文件头不处理
            }

            // 读取所有商品
            while (getline(productsStream, line)) {
                if (line.empty()) continue;

                vector<string> productData = Split(line, ',');
                if (productData.size() >= 6) {
                    allProducts.push_back(productData);
                }
            }

            // 初始时显示所有商品
            currentProducts = allProducts;

        } else {
            // 如果不是PRODUCTS响应，显示实际响应内容
            wstring wResponse = StringToWString("Unexpected response: " + response.substr(0, 100));
            MessageBox(NULL, wResponse.c_str(), L"Debug", MB_OK);
        }
    } catch (const exception& e) {
        string errorMsg = "Error loading products: " + string(e.what());
        MessageBox(NULL, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
    }
}

// 加载购物车数据
void LoadCart() {
    currentCart.clear();
    string response = SendRequest("GET_CART");

    if (response.find("CART") == 0) {
        string cartData = response.substr(5); // 跳过"CART "
        istringstream cartStream(cartData);
        string line;

        // 跳过CSV文件头
        if (getline(cartStream, line)) {
            // 文件头不处理
        }

        // 读取所有购物车项目
        while (getline(cartStream, line)) {
            vector<string> cartData = Split(line, ',');
            if (cartData.size() >= 4) {
                currentCart.push_back(cartData);
            }
        }
    }
}

// 加载订单数据
void LoadOrders() {
    currentOrders.clear();
    string response = SendRequest("GET_ORDERS");

    if (response.find("ORDERS") == 0) {
        string ordersData = response.substr(7); // 跳过"ORDERS "
        istringstream ordersStream(ordersData);
        string line;

        // 跳过CSV文件头
        if (getline(ordersStream, line)) {
            // 文件头不处理
        }

        // 读取所有订单
        while (getline(ordersStream, line)) {
            vector<string> orderData = Split(line, ',');
            if (orderData.size() >= 5) {
                currentOrders.push_back(orderData);
            }
        }
    }
}

// 搜索商品
void SearchProducts(const string& searchTerm) {
    currentProducts.clear();

    if (searchTerm.empty()) {
        currentProducts = allProducts;
        return;
    }

    // 转换搜索词为小写以进行不区分大小写的搜索
    string lowerSearchTerm = searchTerm;
    transform(lowerSearchTerm.begin(), lowerSearchTerm.end(), lowerSearchTerm.begin(), ::tolower);

    for (const auto& product : allProducts) {
        if (product.size() >= 6) {
            // 在商品名称、类型和描述中搜索
            string productName = product[0];
            string productType = product[4];
            string productDesc = product.size() > 5 ? product[5] : "";

            // 转换为小写
            transform(productName.begin(), productName.end(), productName.begin(), ::tolower);
            transform(productType.begin(), productType.end(), productType.begin(), ::tolower);
            transform(productDesc.begin(), productDesc.end(), productDesc.begin(), ::tolower);

            // 检查是否匹配
            if (productName.find(lowerSearchTerm) != string::npos ||
                productType.find(lowerSearchTerm) != string::npos ||
                productDesc.find(lowerSearchTerm) != string::npos) {
                currentProducts.push_back(product);
            }
        }
    }
}

// 重置商品过滤器
void ResetProductsFilter() {
    currentProducts = allProducts;
}

// 获取指定类别的折扣率
double GetDiscountRateForCategory(const string& category) {
    try {
        string request = "GET_DISCOUNT_RATE " + category;
        string response = SendRequest(request);

        if (response.find("DISCOUNT_RATE") == 0) {
            string rateStr = response.substr(14); // 跳过"DISCOUNT_RATE "
            try {
                double rate = stod(rateStr);
                // 验证折扣率范围
                if (rate > 0 && rate <= 1.0) {
                    return rate;
                }
            } catch (...) {
                // 解析失败，返回默认值
            }
        }
    } catch (...) {
        // 请求失败，返回默认值
    }
    return 1.0; // 默认无折扣
}

// 更新商品列表显示
void UpdateProductsList() {
    if (hProductsWnd) {
        HWND hList = GetDlgItem(hProductsWnd, ID_PRODUCTS_LIST);
        SendMessage(hList, LB_RESETCONTENT, 0, 0);

        try {
            for (const auto& product : currentProducts) {
                if (product.size() >= 6) {
                    try {
                        double originalPrice = stod(product[2]);
                        string category = product[4];

                        // 获取该类别的折扣率并计算折扣后价格
                        double discountRate = GetDiscountRateForCategory(category);
                        double discountedPrice = originalPrice * discountRate;

                        string displayText;
                        if (discountRate < 1.0) {
                            // 有折扣时显示原价和折扣价
                            int discountPercent = (int)round((1.0 - discountRate) * 100);
                            displayText = product[0] + " - " + FormatCurrency(originalPrice) +
                                        " → " + FormatCurrency(discountedPrice) +
                                        " (" + to_string(discountPercent) + "% OFF)" +
                                        " (Stock: " + product[3] + ") [" + category + "]";
                        } else {
                            // 无折扣时正常显示
                            displayText = product[0] + " - " + FormatCurrency(originalPrice) +
                                        " (Stock: " + product[3] + ") [" + category + "]";
                        }

                        wstring wDisplayText = StringToWString(displayText);
                        SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
                    } catch (const exception& e) {
                        // 如果价格转换失败，使用原始字符串
                        string displayText = product[0] + " - $" + product[2] +
                                           " (Stock: " + product[3] + ") [" + product[4] + "]";
                        wstring wDisplayText = StringToWString(displayText);
                        SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
                    }
                }
            }

            // 调试信息：显示加载的商品数量
            if (currentProducts.empty()) {
                wstring debugMsg = L"No products loaded";
                SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)debugMsg.c_str());
            }
        } catch (const exception& e) {
            string errorMsg = "Error updating products list: " + string(e.what());
            MessageBox(hProductsWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
        }
    }
}

// 更新购物车列表显示
void UpdateCartList() {
    if (hCartWnd) {
        HWND hList = GetDlgItem(hCartWnd, ID_CART_LIST);
        SendMessage(hList, LB_RESETCONTENT, 0, 0);

        try {
            double totalAmount = 0.0;
            for (const auto& item : currentCart) {
                if (item.size() >= 4) {
                    try {
                        double price = stod(item[1]);
                        int quantity = stoi(item[2]);
                        double itemTotal = price * quantity;
                        totalAmount += itemTotal;

                        string displayText = item[0] + " - " + FormatCurrency(price) +
                                           " x " + item[2] + " = " + FormatCurrency(itemTotal);
                        wstring wDisplayText = StringToWString(displayText);
                        SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
                    } catch (const exception& e) {
                        // 如果转换失败，使用原始字符串
                        string displayText = item[0] + " - $" + item[1] + " x " + item[2];
                        wstring wDisplayText = StringToWString(displayText);
                        SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
                    }
                }
            }

            // 更新总价显示
            string totalText = "Total: " + FormatCurrency(totalAmount);
            SetWindowText(GetDlgItem(hCartWnd, ID_CART_TOTAL), StringToWString(totalText).c_str());
        } catch (const exception& e) {
            string errorMsg = "Error updating cart list: " + string(e.what());
            MessageBox(hCartWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
        }
    }
}

// 更新订单列表显示
void UpdateOrdersList() {
    if (hOrdersWnd) {
        HWND hList = GetDlgItem(hOrdersWnd, ID_ORDERS_LIST);
        SendMessage(hList, LB_RESETCONTENT, 0, 0);

        for (const auto& order : currentOrders) {
            if (order.size() >= 5) {
                string displayText = "Order " + order[0] + " - " + order[2] +
                                   " - " + order[3] + " - " + FormatCurrency(stod(order[4]));
                wstring wDisplayText = StringToWString(displayText);
                SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
            }
        }
    }
}

// 显示登录窗口
void ShowLoginWindow() {
    if (!hLoginWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = LoginWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"LoginWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);

        hLoginWnd = CreateWindow(L"LoginWindow", L"Shopping Platform - Login",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 500, 280,
            NULL, NULL, hInst, NULL);
    }

    ShowWindow(hLoginWnd, SW_SHOW);
    UpdateWindow(hLoginWnd);
    SetForegroundWindow(hLoginWnd);
}

// 显示主窗口
void ShowMainWindow() {
    // 如果主窗口已存在，先销毁它以重新创建适合当前用户类型的界面
    if (hMainWnd) {
        DestroyWindow(hMainWnd);
        hMainWnd = NULL;
    }

    // 注册窗口类（如果还没注册）
    static bool classRegistered = false;
    if (!classRegistered) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = MainWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"MainWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);
        classRegistered = true;
    }

    // 创建新的主窗口
    hMainWnd = CreateWindow(L"MainWindow", L"Shopping Platform - Main Menu",
        WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
        CW_USEDEFAULT, CW_USEDEFAULT, 620, 280,
        NULL, NULL, hInst, NULL);

    if (hMainWnd) {
        UpdateMainWindowInfo();
        ShowWindow(hMainWnd, SW_SHOW);
        UpdateWindow(hMainWnd);
        SetForegroundWindow(hMainWnd);
    }
}

// 显示商品浏览窗口
void ShowProductsWindow() {
    if (!hProductsWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = ProductsWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"ProductsWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);

        hProductsWnd = CreateWindow(L"ProductsWindow", L"Shopping Platform - Products",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 750, 520,
            NULL, NULL, hInst, NULL);
    }

    LoadProducts();
    UpdateProductsList();
    ShowWindow(hMainWnd, SW_HIDE);
    ShowWindow(hProductsWnd, SW_SHOW);
    UpdateWindow(hProductsWnd);
    SetForegroundWindow(hProductsWnd);
}

// 显示购物车窗口
void ShowCartWindow() {
    if (!hCartWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = CartWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"CartWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);

        hCartWnd = CreateWindow(L"CartWindow", L"Shopping Platform - Shopping Cart",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 750, 520,
            NULL, NULL, hInst, NULL);
    }

    LoadCart();
    UpdateCartList();
    ShowWindow(hMainWnd, SW_HIDE);
    ShowWindow(hCartWnd, SW_SHOW);
    UpdateWindow(hCartWnd);
    SetForegroundWindow(hCartWnd);
}

// 显示订单窗口
void ShowOrdersWindow() {
    if (!hOrdersWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = OrdersWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"OrdersWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);

        hOrdersWnd = CreateWindow(L"OrdersWindow", L"Shopping Platform - My Orders",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 750, 480,
            NULL, NULL, hInst, NULL);
    }

    LoadOrders();
    UpdateOrdersList();
    ShowWindow(hMainWnd, SW_HIDE);
    ShowWindow(hOrdersWnd, SW_SHOW);
    UpdateWindow(hOrdersWnd);
    SetForegroundWindow(hOrdersWnd);
}

// 显示充值窗口
void ShowRechargeWindow() {
    if (!hRechargeWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = RechargeWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"RechargeWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);

        hRechargeWnd = CreateWindow(L"RechargeWindow", L"Shopping Platform - Recharge",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 500, 250,
            NULL, NULL, hInst, NULL);
    }

    // 更新余额显示
    UpdateBalance();
    string balanceText = "Current Balance: " + FormatCurrency(currentBalance);
    SetWindowText(GetDlgItem(hRechargeWnd, ID_RECHARGE_STATUS), StringToWString(balanceText).c_str());

    ShowWindow(hMainWnd, SW_HIDE);
    ShowWindow(hRechargeWnd, SW_SHOW);
    UpdateWindow(hRechargeWnd);
    SetForegroundWindow(hRechargeWnd);
}

// 程序入口点
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    hInst = hInstance;

    // 初始化Winsock
    if (!InitializeWinsock()) {
        MessageBox(NULL, L"Failed to initialize Winsock", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // 连接到服务器
    if (!ConnectToServer()) {
        MessageBox(NULL, L"Failed to connect to server. Please make sure the server is running.", L"Connection Error", MB_OK | MB_ICONERROR);
        WSACleanup();
        return 1;
    }

    // 初始化通用控件
    InitCommonControls();

    // 显示登录窗口
    ShowLoginWindow();

    // 消息循环
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // 清理资源
    if (clientSocket != INVALID_SOCKET) {
        SendRequest("LOGOUT"); // 发送登出请求
        closesocket(clientSocket);
    }
    WSACleanup();

    return (int)msg.wParam;
}

// InputBox对话框的窗口过程
LRESULT CALLBACK InputBoxWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建提示标签
            CreateWindow(L"STATIC", L"Enter your password:",
                WS_VISIBLE | WS_CHILD,
                20, 20, 350, 20, hWnd, (HMENU)1001, hInst, NULL);

            // 创建输入框
            CreateWindow(L"EDIT", L"",
                WS_VISIBLE | WS_CHILD | WS_BORDER | ES_PASSWORD,
                20, 50, 350, 25, hWnd, (HMENU)1002, hInst, NULL);

            // 创建确定按钮
            CreateWindow(L"BUTTON", L"OK",
                WS_VISIBLE | WS_CHILD | BS_DEFPUSHBUTTON,
                250, 90, 60, 25, hWnd, (HMENU)IDOK, hInst, NULL);

            // 创建取消按钮
            CreateWindow(L"BUTTON", L"Cancel",
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                320, 90, 60, 25, hWnd, (HMENU)IDCANCEL, hInst, NULL);

            // 设置焦点到输入框
            SetFocus(GetDlgItem(hWnd, 1002));
        }
        break;
    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case IDOK:
            {
                if (g_inputResult && g_inputMaxLength > 0) {
                    GetWindowText(GetDlgItem(hWnd, 1002), g_inputResult, g_inputMaxLength);
                }
                DestroyWindow(hWnd);
                return TRUE;
            }
        case IDCANCEL:
            if (g_inputResult) {
                g_inputResult[0] = L'\0';
            }
            DestroyWindow(hWnd);
            return TRUE;
        }
        break;
    case WM_CLOSE:
        DestroyWindow(hWnd);
        return TRUE;
    case WM_DESTROY:
        // 不要调用PostQuitMessage，因为这会退出整个应用程序
        return TRUE;
    }
    return DefWindowProc(hWnd, message, wParam, lParam);
}

// 简单的InputBox实现
bool InputBox(HWND hParent, const wchar_t* prompt, const wchar_t* title, wchar_t* result, int maxLength) {
    // 设置全局变量
    g_inputResult = result;
    g_inputMaxLength = maxLength;

    // 注册对话框窗口类
    static bool classRegistered = false;
    if (!classRegistered) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = InputBoxWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"InputBoxWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);
        classRegistered = true;
    }

    // 创建模态对话框
    HWND hDlg = CreateWindow(
        L"InputBoxWindow",
        title,
        WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_VISIBLE,
        CW_USEDEFAULT, CW_USEDEFAULT, 400, 150,
        hParent, NULL, hInst, NULL
    );

    if (!hDlg) return false;

    // 禁用父窗口
    if (hParent) {
        EnableWindow(hParent, FALSE);
    }

    // 设置提示文本
    SetWindowText(GetDlgItem(hDlg, 1001), prompt);

    // 设置焦点到输入框
    SetFocus(GetDlgItem(hDlg, 1002));

    // 使用简单的消息循环等待对话框关闭
    MSG msg;
    bool dialogResult = false;

    while (IsWindow(hDlg)) {
        if (GetMessage(&msg, NULL, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        } else {
            break;
        }
    }

    // 检查结果是否有效（非空）
    if (result && wcslen(result) > 0) {
        dialogResult = true;
    }

    // 重新启用父窗口
    if (hParent) {
        EnableWindow(hParent, TRUE);
        SetForegroundWindow(hParent);
    }

    return dialogResult;
}

// 商家商品管理窗口过程
LRESULT CALLBACK MerchantProductsWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Product Management", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 600, 30, hWnd, NULL, hInst, NULL);

            // 创建商品列表框
            CreateWindow(L"LISTBOX", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | LBS_NOTIFY,
                50, 60, 600, 300, hWnd, (HMENU)ID_MERCHANT_PRODUCTS_LIST, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Add Product", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 380, 120, 35, hWnd, (HMENU)ID_MERCHANT_PRODUCTS_ADD, hInst, NULL);
            CreateWindow(L"BUTTON", L"Update Price", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                180, 380, 120, 35, hWnd, (HMENU)ID_MERCHANT_PRODUCTS_UPDATE_PRICE, hInst, NULL);
            CreateWindow(L"BUTTON", L"Update Quantity", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                310, 380, 120, 35, hWnd, (HMENU)ID_MERCHANT_PRODUCTS_UPDATE_QUANTITY, hInst, NULL);
            CreateWindow(L"BUTTON", L"Refresh", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                440, 380, 100, 35, hWnd, (HMENU)ID_MERCHANT_PRODUCTS_REFRESH, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                550, 380, 100, 35, hWnd, (HMENU)ID_MERCHANT_PRODUCTS_BACK, hInst, NULL);

            // 加载商家商品数据
            LoadMerchantProducts();
            UpdateMerchantProductsList();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_MERCHANT_PRODUCTS_ADD:
            ShowAddProductDialog();
            break;
        case ID_MERCHANT_PRODUCTS_UPDATE_PRICE:
            {
                HWND hList = GetDlgItem(hWnd, ID_MERCHANT_PRODUCTS_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                if (selection != LB_ERR && selection < merchantProducts.size()) {
                    string productName = merchantProducts[selection][0];

                    wchar_t newPrice[20] = L"";
                    bool priceEntered = InputBox(hWnd, L"Enter new price:", L"Update Price", newPrice, 20);

                    if (priceEntered && wcslen(newPrice) > 0) {
                        char newPriceA[20];
                        WideCharToMultiByte(CP_UTF8, 0, newPrice, -1, newPriceA, 20, NULL, NULL);

                        double price = atof(newPriceA);
                        if (price > 0) {
                            string updateRequest = "UPDATE_PRODUCT_PRICE \"" + productName + "\" " + to_string(price);
                            string response = SendRequest(updateRequest);

                            if (response == "PRODUCT_UPDATED") {
                                MessageBox(hWnd, L"Price updated successfully!", L"Success", MB_OK | MB_ICONINFORMATION);
                                LoadMerchantProducts();
                                UpdateMerchantProductsList();
                            } else {
                                MessageBox(hWnd, L"Failed to update price", L"Error", MB_OK | MB_ICONERROR);
                            }
                        } else {
                            MessageBox(hWnd, L"Please enter a valid price", L"Invalid Input", MB_OK | MB_ICONWARNING);
                        }
                    }
                } else {
                    MessageBox(hWnd, L"Please select a product first", L"No Selection", MB_OK | MB_ICONWARNING);
                }
            }
            break;
        case ID_MERCHANT_PRODUCTS_UPDATE_QUANTITY:
            {
                HWND hList = GetDlgItem(hWnd, ID_MERCHANT_PRODUCTS_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                if (selection != LB_ERR && selection < merchantProducts.size()) {
                    string productName = merchantProducts[selection][0];

                    wchar_t newQuantity[20] = L"";
                    bool quantityEntered = InputBox(hWnd, L"Enter new quantity:", L"Update Quantity", newQuantity, 20);

                    if (quantityEntered && wcslen(newQuantity) > 0) {
                        int quantity = _wtoi(newQuantity);
                        if (quantity >= 0) {
                            string updateRequest = "UPDATE_PRODUCT_QUANTITY \"" + productName + "\" " + to_string(quantity);
                            string response = SendRequest(updateRequest);

                            if (response == "PRODUCT_UPDATED") {
                                MessageBox(hWnd, L"Quantity updated successfully!", L"Success", MB_OK | MB_ICONINFORMATION);
                                LoadMerchantProducts();
                                UpdateMerchantProductsList();
                            } else {
                                MessageBox(hWnd, L"Failed to update quantity", L"Error", MB_OK | MB_ICONERROR);
                            }
                        } else {
                            MessageBox(hWnd, L"Please enter a valid quantity", L"Invalid Input", MB_OK | MB_ICONWARNING);
                        }
                    }
                } else {
                    MessageBox(hWnd, L"Please select a product first", L"No Selection", MB_OK | MB_ICONWARNING);
                }
            }
            break;
        case ID_MERCHANT_PRODUCTS_REFRESH:
            LoadMerchantProducts();
            UpdateMerchantProductsList();
            break;
        case ID_MERCHANT_PRODUCTS_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_ACTIVATE:
        if (LOWORD(wParam) == WA_ACTIVE || LOWORD(wParam) == WA_CLICKACTIVE) {
            // 窗口激活时自动刷新商品列表
            LoadMerchantProducts();
            UpdateMerchantProductsList();
            UpdateMainWindowInfo(); // 更新主窗口的余额显示
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 商家订单管理窗口过程
LRESULT CALLBACK MerchantOrdersWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Received Orders & Payment Status", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 600, 30, hWnd, NULL, hInst, NULL);

            // 创建订单列表框
            CreateWindow(L"LISTBOX", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | LBS_NOTIFY,
                50, 60, 600, 300, hWnd, (HMENU)ID_MERCHANT_ORDERS_LIST, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"View Details", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 380, 120, 35, hWnd, (HMENU)ID_MERCHANT_ORDERS_DETAILS, hInst, NULL);
            CreateWindow(L"BUTTON", L"Refresh", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                180, 380, 120, 35, hWnd, (HMENU)ID_MERCHANT_ORDERS_REFRESH, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                530, 380, 120, 35, hWnd, (HMENU)ID_MERCHANT_ORDERS_BACK, hInst, NULL);

            // 加载商家订单数据
            LoadMerchantOrders();
            UpdateMerchantOrdersList();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_MERCHANT_ORDERS_DETAILS:
            {
                HWND hList = GetDlgItem(hWnd, ID_MERCHANT_ORDERS_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                if (selection != LB_ERR && selection < merchantOrders.size()) {
                    string orderId = merchantOrders[selection][0];
                    string detailsRequest = "GET_ORDER_DETAILS " + orderId;
                    string response = SendRequest(detailsRequest);

                    if (response.find("ORDER_DETAILS") == 0) {
                        string details = "Order ID: " + orderId + "\n";
                        details += "Customer: " + merchantOrders[selection][1] + "\n";
                        details += "Date: " + merchantOrders[selection][2] + "\n";
                        details += "Status: " + merchantOrders[selection][3] + "\n";
                        details += "Total: $" + merchantOrders[selection][4];

                        MessageBox(hWnd, StringToWString(details).c_str(), L"Order Details", MB_OK | MB_ICONINFORMATION);
                    } else {
                        MessageBox(hWnd, L"Failed to retrieve order details", L"Error", MB_OK | MB_ICONERROR);
                    }
                } else {
                    MessageBox(hWnd, L"Please select an order to view details", L"No Selection", MB_OK | MB_ICONWARNING);
                }
            }
            break;
        case ID_MERCHANT_ORDERS_REFRESH:
            LoadMerchantOrders();
            UpdateMerchantOrdersList();
            break;
        case ID_MERCHANT_ORDERS_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_ACTIVATE:
        if (LOWORD(wParam) == WA_ACTIVE || LOWORD(wParam) == WA_CLICKACTIVE) {
            // 窗口激活时自动刷新订单列表和余额
            LoadMerchantOrders();
            UpdateMerchantOrdersList();
            UpdateMainWindowInfo(); // 更新主窗口的余额显示
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 添加商品对话框窗口过程
LRESULT CALLBACK AddProductWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Add New Product", WS_VISIBLE | WS_CHILD | SS_CENTER,
                20, 20, 360, 25, hWnd, NULL, hInst, NULL);

            // 创建商品名称输入
            CreateWindow(L"STATIC", L"Product Name:", WS_VISIBLE | WS_CHILD,
                20, 60, 100, 20, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
                130, 60, 250, 25, hWnd, (HMENU)ID_ADD_PRODUCT_NAME, hInst, NULL);

            // 创建商品描述输入
            CreateWindow(L"STATIC", L"Description:", WS_VISIBLE | WS_CHILD,
                20, 100, 100, 20, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
                130, 100, 250, 25, hWnd, (HMENU)ID_ADD_PRODUCT_DESC, hInst, NULL);

            // 创建价格输入
            CreateWindow(L"STATIC", L"Price:", WS_VISIBLE | WS_CHILD,
                20, 140, 100, 20, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
                130, 140, 100, 25, hWnd, (HMENU)ID_ADD_PRODUCT_PRICE, hInst, NULL);

            // 创建数量输入
            CreateWindow(L"STATIC", L"Quantity:", WS_VISIBLE | WS_CHILD,
                20, 180, 100, 20, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | ES_NUMBER,
                130, 180, 100, 25, hWnd, (HMENU)ID_ADD_PRODUCT_QUANTITY, hInst, NULL);

            // 创建类型选择
            CreateWindow(L"STATIC", L"Type:", WS_VISIBLE | WS_CHILD,
                20, 220, 100, 20, hWnd, NULL, hInst, NULL);
            HWND hCombo = CreateWindow(L"COMBOBOX", L"",
                WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
                130, 220, 150, 200, hWnd, (HMENU)ID_ADD_PRODUCT_TYPE, hInst, NULL);

            // 添加商品类型选项
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Book");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Clothing");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Food");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"DailyNecessities");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"PersonalCare");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Electronics");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Beverages");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"MaternityAndBabyProducts");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Others");
            SendMessage(hCombo, CB_SETCURSEL, 0, 0); // 默认选择第一项

            // 创建按钮
            CreateWindow(L"BUTTON", L"Add Product", WS_VISIBLE | WS_CHILD | BS_DEFPUSHBUTTON,
                130, 270, 100, 30, hWnd, (HMENU)ID_ADD_PRODUCT_OK, hInst, NULL);
            CreateWindow(L"BUTTON", L"Cancel", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                240, 270, 100, 30, hWnd, (HMENU)ID_ADD_PRODUCT_CANCEL, hInst, NULL);

            // 设置焦点到商品名称输入框
            SetFocus(GetDlgItem(hWnd, ID_ADD_PRODUCT_NAME));
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_ADD_PRODUCT_OK:
            {
                // 获取输入的数据
                wchar_t name[256], desc[256], priceText[20], quantityText[20], type[50];
                GetWindowText(GetDlgItem(hWnd, ID_ADD_PRODUCT_NAME), name, 256);
                GetWindowText(GetDlgItem(hWnd, ID_ADD_PRODUCT_DESC), desc, 256);
                GetWindowText(GetDlgItem(hWnd, ID_ADD_PRODUCT_PRICE), priceText, 20);
                GetWindowText(GetDlgItem(hWnd, ID_ADD_PRODUCT_QUANTITY), quantityText, 20);

                // 获取选中的类型
                HWND hCombo = GetDlgItem(hWnd, ID_ADD_PRODUCT_TYPE);
                int typeIndex = SendMessage(hCombo, CB_GETCURSEL, 0, 0);
                SendMessage(hCombo, CB_GETLBTEXT, typeIndex, (LPARAM)type);

                // 验证输入
                if (wcslen(name) == 0) {
                    MessageBox(hWnd, L"Please enter product name", L"Invalid Input", MB_OK | MB_ICONWARNING);
                    break;
                }
                if (wcslen(desc) == 0) {
                    MessageBox(hWnd, L"Please enter product description", L"Invalid Input", MB_OK | MB_ICONWARNING);
                    break;
                }
                if (wcslen(priceText) == 0) {
                    MessageBox(hWnd, L"Please enter product price", L"Invalid Input", MB_OK | MB_ICONWARNING);
                    break;
                }
                if (wcslen(quantityText) == 0) {
                    MessageBox(hWnd, L"Please enter product quantity", L"Invalid Input", MB_OK | MB_ICONWARNING);
                    break;
                }

                double price = _wtof(priceText);
                int quantity = _wtoi(quantityText);

                if (price <= 0) {
                    MessageBox(hWnd, L"Please enter a valid price (greater than 0)", L"Invalid Input", MB_OK | MB_ICONWARNING);
                    break;
                }
                if (quantity < 0) {
                    MessageBox(hWnd, L"Please enter a valid quantity (0 or greater)", L"Invalid Input", MB_OK | MB_ICONWARNING);
                    break;
                }

                // 转换为string
                char nameA[256], descA[256], typeA[50];
                WideCharToMultiByte(CP_UTF8, 0, name, -1, nameA, 256, NULL, NULL);
                WideCharToMultiByte(CP_UTF8, 0, desc, -1, descA, 256, NULL, NULL);
                WideCharToMultiByte(CP_UTF8, 0, type, -1, typeA, 50, NULL, NULL);

                // 发送添加商品请求
                string addRequest = "ADD_PRODUCT \"" + string(nameA) + "\" \"" + string(descA) + "\" " +
                                  to_string(price) + " " + to_string(quantity) + " " + string(typeA);
                string response = SendRequest(addRequest);

                if (response == "PRODUCT_ADDED") {
                    MessageBox(hWnd, L"Product added successfully!", L"Success", MB_OK | MB_ICONINFORMATION);
                    DestroyWindow(hWnd);

                    // 刷新商家商品列表
                    if (hMerchantProductsWnd) {
                        LoadMerchantProducts();
                        UpdateMerchantProductsList();
                    }
                } else {
                    string errorMsg = "Failed to add product: " + response;
                    MessageBox(hWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
                }
            }
            break;
        case ID_ADD_PRODUCT_CANCEL:
            DestroyWindow(hWnd);
            break;
        }
        break;

    case WM_CLOSE:
        DestroyWindow(hWnd);
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 加载商家商品数据
void LoadMerchantProducts() {
    merchantProducts.clear();
    string response = SendRequest("GET_MERCHANT_PRODUCTS");

    if (response.find("PRODUCTS") == 0) {
        string productsData = response.substr(9); // 跳过"PRODUCTS "
        istringstream productsStream(productsData);
        string line;

        // 跳过CSV文件头
        if (getline(productsStream, line)) {
            // 文件头不处理
        }

        // 读取所有商品
        while (getline(productsStream, line)) {
            if (line.empty()) continue;
            vector<string> productData = Split(line, ',');
            if (productData.size() >= 6) {
                merchantProducts.push_back(productData);
            }
        }
    }
}

// 加载商家订单数据
void LoadMerchantOrders() {
    merchantOrders.clear();
    string response = SendRequest("GET_MERCHANT_ORDERS");

    if (response.find("ORDERS") == 0) {
        string ordersData = response.substr(7); // 跳过"ORDERS "
        istringstream ordersStream(ordersData);
        string line;

        // 跳过CSV文件头
        if (getline(ordersStream, line)) {
            // 文件头不处理
        }

        // 读取所有订单
        while (getline(ordersStream, line)) {
            if (line.empty()) continue;
            vector<string> orderData = Split(line, ',');
            if (orderData.size() >= 4) {  // 商家订单格式：orderId,consumer,date,amount,status
                merchantOrders.push_back(orderData);
            }
        }
    }
}

// 更新商家商品列表显示
void UpdateMerchantProductsList() {
    if (hMerchantProductsWnd) {
        HWND hList = GetDlgItem(hMerchantProductsWnd, ID_MERCHANT_PRODUCTS_LIST);
        SendMessage(hList, LB_RESETCONTENT, 0, 0);

        try {
            for (const auto& product : merchantProducts) {
                if (product.size() >= 6) {
                    try {
                        double price = stod(product[2]);
                        string displayText = product[0] + " - " + FormatCurrency(price) +
                                           " (Stock: " + product[3] + ") [" + product[4] + "]";
                        wstring wDisplayText = StringToWString(displayText);
                        SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
                    } catch (const exception& e) {
                        // 如果价格转换失败，使用原始字符串
                        string displayText = product[0] + " - $" + product[2] +
                                           " (Stock: " + product[3] + ") [" + product[4] + "]";
                        wstring wDisplayText = StringToWString(displayText);
                        SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
                    }
                }
            }

            // 如果没有商品，显示提示信息
            if (merchantProducts.empty()) {
                wstring noProductsMsg = L"No products found. Click 'Add Product' to add your first product.";
                SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)noProductsMsg.c_str());
            }
        } catch (const exception& e) {
            string errorMsg = "Error updating merchant products list: " + string(e.what());
            MessageBox(hMerchantProductsWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
        }
    }
}

// 更新商家订单列表显示
void UpdateMerchantOrdersList() {
    if (hMerchantOrdersWnd) {
        HWND hList = GetDlgItem(hMerchantOrdersWnd, ID_MERCHANT_ORDERS_LIST);
        SendMessage(hList, LB_RESETCONTENT, 0, 0);

        try {
            for (const auto& order : merchantOrders) {
                if (order.size() >= 4) {  // 商家订单格式：orderId,consumer,date,amount,status
                    try {
                        double amount = stod(order[3]);  // 金额在第4列（索引3）
                        string status = order.size() >= 5 ? order[4] : "unknown";  // 状态在第5列（索引4）

                        // 根据状态显示不同的标识
                        string statusDisplay;
                        if (status == "paid") {
                            statusDisplay = " ✓ PAID";
                        } else if (status == "unpaid") {
                            statusDisplay = " ⏳ UNPAID";
                        } else {
                            statusDisplay = " ? " + status;
                        }

                        string displayText = "Order " + order[0] + " - Customer: " + order[1] +
                                           " - " + order[2] + " - " + FormatCurrency(amount) + statusDisplay;
                        wstring wDisplayText = StringToWString(displayText);
                        SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
                    } catch (const exception& e) {
                        // 如果金额转换失败，使用原始字符串
                        string status = order.size() >= 5 ? order[4] : "unknown";
                        string statusDisplay;
                        if (status == "paid") {
                            statusDisplay = " ✓ PAID";
                        } else if (status == "unpaid") {
                            statusDisplay = " ⏳ UNPAID";
                        } else {
                            statusDisplay = " ? " + status;
                        }

                        string displayText = "Order " + order[0] + " - Customer: " + order[1] +
                                           " - " + order[2] + " - $" + order[3] + statusDisplay;
                        wstring wDisplayText = StringToWString(displayText);
                        SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
                    }
                }
            }

            // 如果没有订单，显示提示信息
            if (merchantOrders.empty()) {
                wstring noOrdersMsg = L"No orders received yet.";
                SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)noOrdersMsg.c_str());
            }
        } catch (const exception& e) {
            string errorMsg = "Error updating merchant orders list: " + string(e.what());
            MessageBox(hMerchantOrdersWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
        }
    }
}

// 显示商家商品管理窗口
void ShowMerchantProductsWindow() {
    if (!hMerchantProductsWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = MerchantProductsWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"MerchantProductsWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);

        hMerchantProductsWnd = CreateWindow(L"MerchantProductsWindow", L"Product Management",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 750, 480,
            NULL, NULL, hInst, NULL);
    }

    LoadMerchantProducts();
    UpdateMerchantProductsList();
    ShowWindow(hMainWnd, SW_HIDE);
    ShowWindow(hMerchantProductsWnd, SW_SHOW);
    UpdateWindow(hMerchantProductsWnd);
    SetForegroundWindow(hMerchantProductsWnd);
}

// 显示商家订单管理窗口
void ShowMerchantOrdersWindow() {
    if (!hMerchantOrdersWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = MerchantOrdersWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"MerchantOrdersWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);

        hMerchantOrdersWnd = CreateWindow(L"MerchantOrdersWindow", L"Received Orders",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 750, 480,
            NULL, NULL, hInst, NULL);
    }

    LoadMerchantOrders();
    UpdateMerchantOrdersList();
    ShowWindow(hMainWnd, SW_HIDE);
    ShowWindow(hMerchantOrdersWnd, SW_SHOW);
    UpdateWindow(hMerchantOrdersWnd);
    SetForegroundWindow(hMerchantOrdersWnd);
}

// 显示添加商品对话框
void ShowAddProductDialog() {
    // 注册对话框窗口类
    static bool classRegistered = false;
    if (!classRegistered) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = AddProductWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"AddProductWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        RegisterClass(&wc);
        classRegistered = true;
    }

    // 创建模态对话框
    HWND hDlg = CreateWindow(
        L"AddProductWindow",
        L"Add New Product",
        WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_VISIBLE,
        CW_USEDEFAULT, CW_USEDEFAULT, 420, 350,
        hMerchantProductsWnd, NULL, hInst, NULL
    );

    if (hDlg) {
        // 禁用父窗口
        if (hMerchantProductsWnd) {
            EnableWindow(hMerchantProductsWnd, FALSE);
        }

        // 设置焦点到对话框
        SetForegroundWindow(hDlg);

        // 使用简单的消息循环等待对话框关闭
        MSG msg;
        while (IsWindow(hDlg)) {
            if (GetMessage(&msg, NULL, 0, 0)) {
                TranslateMessage(&msg);
                DispatchMessage(&msg);
            } else {
                break;
            }
        }

        // 重新启用父窗口
        if (hMerchantProductsWnd) {
            EnableWindow(hMerchantProductsWnd, TRUE);
            SetForegroundWindow(hMerchantProductsWnd);
        }
    }
}

// 折扣管理相关函数实现

// 加载折扣数据
void LoadDiscounts() {
    currentDiscounts.clear();

    try {
        string response = SendRequest("GET_DISCOUNTS");

        if (response.find("DISCOUNTS") == 0) {
            string discountData = response.substr(10); // 跳过"DISCOUNTS "
            istringstream iss(discountData);
            string line;

            // 跳过CSV头部
            bool isFirstLine = true;
            while (getline(iss, line)) {
                if (!line.empty()) {
                    if (isFirstLine) {
                        // 跳过CSV头部 "category,discount_rate"
                        isFirstLine = false;
                        continue;
                    }

                    vector<string> discountInfo = Split(line, ',');
                    if (discountInfo.size() >= 2) {
                        // 验证数据格式
                        try {
                            double rate = stod(discountInfo[1]);
                            if (rate > 0 && rate <= 1.0) {
                                currentDiscounts.push_back(discountInfo);
                            }
                        } catch (...) {
                            // 忽略无效的折扣数据
                            continue;
                        }
                    }
                }
            }
        }
    } catch (const exception& e) {
        // 如果加载失败，确保currentDiscounts为空
        currentDiscounts.clear();
        if (hDiscountWnd) {
            string errorMsg = "Error loading discounts: " + string(e.what());
            MessageBox(hDiscountWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
        }
    }
}

// 更新折扣列表显示
void UpdateDiscountsList() {
    if (!hDiscountWnd) return;

    HWND hList = GetDlgItem(hDiscountWnd, ID_DISCOUNT_LIST);
    SendMessage(hList, LB_RESETCONTENT, 0, 0);

    try {
        if (currentDiscounts.empty()) {
            // 如果没有折扣数据，显示提示信息
            SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)L"No discount information available. Set discounts below.");
            return;
        }

        for (size_t i = 0; i < currentDiscounts.size(); i++) {
            if (currentDiscounts[i].size() >= 2) {
                try {
                    string category = currentDiscounts[i][0];
                    double rate = stod(currentDiscounts[i][1]);
                    // 使用round函数确保正确的四舍五入
                    int discountPercent = (int)round((1.0 - rate) * 100);

                    string displayText;
                    if (rate >= 1.0) {
                        displayText = category + " - No discount";
                    } else {
                        displayText = category + " - " + to_string(discountPercent) + "% off (Pay " + to_string((int)round(rate * 100)) + "%)";
                    }

                    SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)StringToWString(displayText).c_str());
                } catch (const exception& e) {
                    // 跳过无效的折扣条目
                    continue;
                }
            }
        }
    } catch (const exception& e) {
        string errorMsg = "Error updating discounts list: " + string(e.what());
        MessageBox(hDiscountWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
    }
}

// 显示折扣管理窗口
void ShowDiscountWindow() {
    if (!hDiscountWnd) {
        // 注册窗口类
        static bool classRegistered = false;
        if (!classRegistered) {
            WNDCLASS wc = {};
            wc.lpfnWndProc = DiscountWndProc;
            wc.hInstance = hInst;
            wc.lpszClassName = L"DiscountWindow";
            wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
            wc.hCursor = LoadCursor(NULL, IDC_ARROW);
            RegisterClass(&wc);
            classRegistered = true;
        }

        hDiscountWnd = CreateWindow(
            L"DiscountWindow",
            L"Manage Discounts",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT, 700, 500,
            NULL, NULL, hInst, NULL
        );
    }

    if (hDiscountWnd) {
        LoadDiscounts();
        UpdateDiscountsList();
        ShowWindow(hMainWnd, SW_HIDE);
        ShowWindow(hDiscountWnd, SW_SHOW);
        SetForegroundWindow(hDiscountWnd);
    }
}

// 折扣管理窗口过程
LRESULT CALLBACK DiscountWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Manage Product Discounts",
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 600, 30, hWnd, NULL, hInst, NULL);

            // 创建折扣列表框
            CreateWindow(L"LISTBOX", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | LBS_NOTIFY,
                50, 60, 600, 250, hWnd, (HMENU)ID_DISCOUNT_LIST, hInst, NULL);

            // 创建设置折扣区域
            CreateWindow(L"STATIC", L"Set Discount for Category:",
                WS_VISIBLE | WS_CHILD,
                50, 330, 200, 25, hWnd, NULL, hInst, NULL);

            // 创建类别下拉框
            HWND hCombo = CreateWindow(L"COMBOBOX", L"",
                WS_VISIBLE | WS_CHILD | WS_BORDER | CBS_DROPDOWNLIST,
                50, 360, 150, 200, hWnd, (HMENU)ID_DISCOUNT_CATEGORY, hInst, NULL);

            // 添加商品类别选项
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Book");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Food");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Clothing");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"DailyNecessities");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"PersonalCare");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Electronics");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Beverages");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"MaternityAndBabyProducts");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Appliances");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"StationeryAndSportsGoods");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Toys");
            SendMessage(hCombo, CB_ADDSTRING, 0, (LPARAM)L"Others");

            // 创建折扣率输入框
            CreateWindow(L"STATIC", L"Discount Rate (0.1-1.0):",
                WS_VISIBLE | WS_CHILD,
                220, 330, 150, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"1.0", WS_VISIBLE | WS_CHILD | WS_BORDER,
                220, 360, 100, 25, hWnd, (HMENU)ID_DISCOUNT_RATE, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Set Discount", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                340, 360, 100, 25, hWnd, (HMENU)ID_DISCOUNT_SET, hInst, NULL);
            CreateWindow(L"BUTTON", L"Refresh", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 400, 100, 35, hWnd, (HMENU)ID_DISCOUNT_REFRESH, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                550, 400, 100, 35, hWnd, (HMENU)ID_DISCOUNT_BACK, hInst, NULL);

            // 加载折扣数据
            LoadDiscounts();
            UpdateDiscountsList();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_DISCOUNT_SET:
            {
                // 获取选中的类别
                HWND hCombo = GetDlgItem(hWnd, ID_DISCOUNT_CATEGORY);
                int selection = SendMessage(hCombo, CB_GETCURSEL, 0, 0);
                if (selection == CB_ERR) {
                    MessageBox(hWnd, L"Please select a category first", L"No Selection", MB_OK | MB_ICONWARNING);
                    break;
                }

                wchar_t categoryText[256];
                SendMessage(hCombo, CB_GETLBTEXT, selection, (LPARAM)categoryText);

                // 获取折扣率
                wchar_t rateText[10];
                GetWindowText(GetDlgItem(hWnd, ID_DISCOUNT_RATE), rateText, 10);

                // 转换为string
                char categoryA[256], rateA[10];
                WideCharToMultiByte(CP_UTF8, 0, categoryText, -1, categoryA, 256, NULL, NULL);
                WideCharToMultiByte(CP_UTF8, 0, rateText, -1, rateA, 10, NULL, NULL);

                // 验证折扣率
                double rate;
                try {
                    rate = stod(string(rateA));
                } catch (...) {
                    MessageBox(hWnd, L"Invalid discount rate format", L"Error", MB_OK | MB_ICONERROR);
                    break;
                }

                if (rate <= 0 || rate > 1.0) {
                    MessageBox(hWnd, L"Discount rate must be between 0.01 and 1.0", L"Invalid Rate", MB_OK | MB_ICONWARNING);
                    break;
                }

                // 发送设置折扣请求
                string setRequest = "SET_DISCOUNT " + string(categoryA) + " " + to_string(rate);
                string response = SendRequest(setRequest);

                if (response == "DISCOUNT_SET") {
                    int discountPercent = (int)round((1.0 - rate) * 100);
                    string successMsg;
                    if (rate >= 1.0) {
                        successMsg = "Discount set successfully! " + string(categoryA) + " now has no discount.";
                    } else {
                        successMsg = "Discount set successfully! " + string(categoryA) + " now has " + to_string(discountPercent) + "% off (customers pay " + to_string((int)round(rate * 100)) + "%).";
                    }
                    MessageBox(hWnd, StringToWString(successMsg).c_str(), L"Success", MB_OK | MB_ICONINFORMATION);

                    // 刷新列表
                    LoadDiscounts();
                    UpdateDiscountsList();
                } else {
                    string errorMsg = "Failed to set discount: " + response;
                    MessageBox(hWnd, StringToWString(errorMsg).c_str(), L"Error", MB_OK | MB_ICONERROR);
                }
            }
            break;
        case ID_DISCOUNT_REFRESH:
            LoadDiscounts();
            UpdateDiscountsList();
            break;
        case ID_DISCOUNT_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_ACTIVATE:
        if (LOWORD(wParam) == WA_ACTIVE || LOWORD(wParam) == WA_CLICKACTIVE) {
            // 窗口激活时自动刷新折扣列表
            LoadDiscounts();
            UpdateDiscountsList();
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}