# Shopping Platform GUI - 快速开始指南

## 🚀 快速启动

### 方法1：一键启动（推荐）
```bash
# 双击运行
start_gui.bat
```

### 方法2：手动启动
1. 编译GUI客户端：
   ```bash
   compile_gui.bat
   ```

2. 启动服务器：
   ```bash
   Server.exe
   ```

3. 启动GUI客户端：
   ```bash
   ClientGUI_Enhanced.exe
   ```

## 📋 系统要求

- Windows 7 或更高版本
- MinGW-w64 或 Visual Studio
- 网络连接（本地回环）

## 🔑 默认账户

### 消费者账户
- **用户名**: Orange_Ice
- **密码**: password（或查看现有数据文件）

### 商家账户
- **用户名**: Xuan
- **密码**: password（或查看现有数据文件）

## 🎯 主要功能

### 登录界面
- 输入用户名和密码
- 点击"Login"登录
- 实时状态提示

### 主菜单
- **Browse Products**: 浏览所有商品
- **Shopping Cart**: 管理购物车
- **My Orders**: 查看和管理订单
- **Check Balance**: 查询账户余额
- **Recharge**: 账户充值
- **Logout**: 安全退出

### 商品浏览
- 查看商品列表（名称、价格、库存、类型）
- 选择购买数量
- 添加到购物车
- 实时库存显示

### 购物车管理
- 查看购物车商品
- 实时总价计算
- 移除商品
- 生成订单

### 订单管理
- 查看所有订单
- 订单状态显示
- 在线支付
- 订单详情查看

### 账户管理
- 实时余额显示
- 在线充值
- 余额验证

## 🎨 界面特色

### 用户友好设计
- 直观的图形界面
- 清晰的按钮布局
- 实时信息更新
- 友好的错误提示

### 功能完整性
- 完整的购物流程
- 实时数据同步
- 多窗口管理
- 状态保持

### 安全特性
- 登录验证
- 余额检查
- 操作确认
- 错误处理

## 🔧 故障排除

### 连接问题
**问题**: "Failed to connect to server"
**解决**: 
1. 确保服务器正在运行
2. 检查防火墙设置
3. 验证端口8888未被占用

### 编译问题
**问题**: 编译失败
**解决**:
1. 确保安装了MinGW-w64
2. 检查PATH环境变量
3. 验证所有源文件存在

### 登录问题
**问题**: 登录失败
**解决**:
1. 检查用户名和密码
2. 确保用户已注册
3. 使用控制台版本注册新用户

### 数据问题
**问题**: 数据不显示
**解决**:
1. 点击"Refresh"按钮
2. 重新登录
3. 检查服务器日志

## 📁 文件说明

### GUI相关文件
- `ClientGUI.cpp` - 基础GUI客户端源码
- `ClientGUI_Enhanced.cpp` - 增强版GUI客户端源码
- `ClientGUI.exe` - 基础版可执行文件
- `ClientGUI_Enhanced.exe` - 增强版可执行文件

### 编译和启动脚本
- `compile_gui.bat` - GUI编译脚本
- `start_gui.bat` - 一键启动脚本

### 文档
- `GUI_README.md` - 详细说明文档
- `GUI_Quick_Start.md` - 快速开始指南

## 🆘 技术支持

### 常见问题
1. **Q**: GUI版本支持注册吗？
   **A**: 目前GUI版本不支持注册，请使用控制台版本注册新用户。

2. **Q**: 可以同时运行多个GUI客户端吗？
   **A**: 可以，但需要使用不同的用户账户登录。

3. **Q**: 如何查看服务器状态？
   **A**: 服务器运行时会显示控制台窗口，可以查看连接和请求日志。

4. **Q**: 数据保存在哪里？
   **A**: 数据保存在CSV文件中（users.csv, products.csv, orders.csv等）。

### 联系方式
如遇到技术问题，请：
1. 检查服务器控制台输出
2. 查看错误消息
3. 参考文档说明
4. 重启系统组件

## 🎉 享受购物体验！

现在您可以使用图形化界面享受完整的购物平台体验了！
