#define _WIN32_WINNT 0x0600  // 确保 Windows Vista 或更高版本，以支持 inet_pton

#include <iostream>
#include <string>
#include <sstream>
#include <vector>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <conio.h>
#include <iomanip>

#pragma comment(lib, "ws2_32.lib")

using namespace std;

// 客户端配置
const string SERVER_IP = "127.0.0.1";
const int PORT = 8888;
const int BUFFER_SIZE = 4096;

// 全局变量
SOCKET clientSocket;
string currentUsername;
string userType;

// 辅助函数：分割字符串
vector<string> split(const string& s, char delimiter) {
    vector<string> tokens;
    string token;
    istringstream tokenStream(s);
    while (getline(tokenStream, token, delimiter)) {
        tokens.push_back(token);
    }
    return tokens;
}

// 发送请求并接收响应
string sendRequest(const string& request) {
    // 支付请求需要更长的超时时间
    int timeout;
    if (request.find("PAY_ORDER") == 0) {
        timeout = 15000; // 支付请求使用15秒超时
    } else {
        timeout = 5000; // 其他请求使用5秒超时
    }

    // 设置发送超时
    setsockopt(clientSocket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));

    // 发送请求
    int result = send(clientSocket, request.c_str(), request.length(), 0);
    if (result == SOCKET_ERROR) {
        cerr << "Send failed: " << WSAGetLastError() << endl;
        return "ERROR_SEND_FAILED";
    }

    // 设置接收超时
    setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));

    // 接收响应
    char buffer[BUFFER_SIZE];
    memset(buffer, 0, BUFFER_SIZE);
    result = recv(clientSocket, buffer, BUFFER_SIZE, 0);

    if (result <= 0) {
        int error = WSAGetLastError();
        if (error == WSAETIMEDOUT) {
            cerr << "Receive timed out. Server may be busy." << endl;

            // 如果是支付请求超时，可能是余额不足
            if (request.find("PAY_ORDER") == 0) {
                return "ERROR_PAYMENT_TIMEOUT";
            } else {
                return "ERROR_TIMEOUT";
            }
        } else {
            cerr << "Receive failed or connection closed: " << error << endl;
            return "ERROR_RECEIVE_FAILED";
        }
    }

    // 不再打印接收到的响应
    return string(buffer);
}

// 函数前向声明
void loginScreen();
void registerScreen();
void mainMenuScreen();
void userMenuScreen();
void changePasswordScreen();
void checkBalanceScreen();
void rechargeScreen();
void displayProductsScreen();
void searchProductsScreen();
void shoppingCartScreen();
void viewOrdersScreen();
void generateOrderScreen();
void payOrderScreen(const string& orderId);
void manageProductsScreen();
void viewReceivedOrdersScreen();
void addProductScreen();
void updateProductPriceScreen();
void updateProductQuantityScreen();
void viewMyProductsScreen();
void viewMerchantOrderDetailsScreen(const string& orderId);
string getOrderPaymentStatus(const string& orderId);
void viewRevenueStatisticsScreen();
void manageCategoryDiscountsScreen();
void viewDiscountsScreen();
void setDiscountScreen();

// 用户菜单界面
void userMenuScreen() {
    while (true) {
        system("cls");
        cout << "\n===== User Menu =====\n";
        cout << "Welcome, " << currentUsername << " (" << userType << ")!\n";

        // 实时显示当前余额
        string balanceRequest = "GET_BALANCE";
        string balanceResponse = sendRequest(balanceRequest);
        double currentBalance = 0.0;
        if (balanceResponse.find("BALANCE") == 0) {
            string balanceStr = balanceResponse.substr(8); // 跳过"BALANCE "
            currentBalance = stod(balanceStr);
        }
        cout << "Current Balance: $" << currentBalance << "\n\n";

        if (userType == "consumer") {
            cout << "1. Change Password\n";
            cout << "2. Check Balance\n";
            cout << "3. Recharge\n";
            cout << "4. Display Products\n";
            cout << "5. Search Products\n";
            cout << "6. Shopping Cart\n";
            cout << "7. View Orders\n";
            cout << "8. Logout\n";
            cout << "9. Exit\n";
        } else if (userType == "merchant") {
            cout << "1. Change Password\n";
            cout << "2. Check Balance\n";
            cout << "3. Recharge\n";
            cout << "4. Display Products\n";
            cout << "5. Search Products\n";
            cout << "6. Manage My Products\n";
            cout << "7. View Received Orders\n";
            cout << "8. Logout\n";
            cout << "9. Exit\n";
        }
        cout << "Please enter your choice: ";

        int choice;
        cin >> choice;
        cin.ignore();

        if (userType == "consumer") {
            switch (choice) {
                case 1:
                    changePasswordScreen();
                    break;
                case 2:
                    checkBalanceScreen();
                    break;
                case 3:
                    rechargeScreen();
                    break;
                case 4:
                    displayProductsScreen();
                    break;
                case 5:
                    searchProductsScreen();
                    break;
                case 6:
                    shoppingCartScreen();
                    break;
                case 7:
                    viewOrdersScreen();
                    break;
                case 8:
                    // 登出
                    sendRequest("LOGOUT");
                    currentUsername = "";
                    userType = "";
                    cout << "Logged out successfully." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
                    return;
                case 9:
                    cout << "Thank you for using our platform. Goodbye!" << endl;
                    exit(0);
                default:
                    cout << "Invalid choice. Please try again." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
            }
        } else if (userType == "merchant") {
            switch (choice) {
                case 1:
                    changePasswordScreen();
                    break;
                case 2:
                    checkBalanceScreen();
                    break;
                case 3:
                    rechargeScreen();
                    break;
                case 4:
                    displayProductsScreen();
                    break;
                case 5:
                    searchProductsScreen();
                    break;
                case 6:
                    // 管理我的商品
                    manageProductsScreen();
                    break;
                case 7:
                    // 查看收到的订单 - 添加余额刷新提示
                    cout << "Loading orders and checking for new payments..." << endl;
                    viewReceivedOrdersScreen();
                    break;
                case 8:
                    // 登出
                    sendRequest("LOGOUT");
                    currentUsername = "";
                    userType = "";
                    cout << "Logged out successfully." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
                    return;
                case 9:
                    cout << "Thank you for using our platform. Goodbye!" << endl;
                    exit(0);
                default:
                    cout << "Invalid choice. Please try again." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
            }
        }
    }
}

// 显示商品界面
void displayProductsScreen() {
    system("cls");
    cout << "\n===== Products =====\n";

    // 发送获取商品请求
    string request = "GET_PRODUCTS";
    string response = sendRequest(request);

    vector<string> productNames;
    bool productsLoaded = false;

    if (response.find("PRODUCTS") == 0) {
        // 解析并显示商品信息
        string productsData = response.substr(9); // 跳过"PRODUCTS "
        istringstream productsStream(productsData);
        string line;

        cout << "Available products:\n";
        int index = 1;

        // 跳过CSV文件头
        if (getline(productsStream, line)) {
            cout << "0. " << line << endl;
        }

        // 显示所有商品，并为每个商品添加索引
        while (getline(productsStream, line)) {
            cout << index << ". " << line << endl;

            // 提取商品名称 - 从格式化的输出中提取
            size_t nameStart = line.find("Name: ");
            size_t nameEnd = line.find(", Description:");

            if (nameStart != string::npos && nameEnd != string::npos) {
                // 提取名称，跳过"Name: "前缀
                string productName = line.substr(nameStart + 6, nameEnd - (nameStart + 6));
                productNames.push_back(productName);
            }

            index++;
        }

        productsLoaded = (index > 1);
    } else {
        cout << "Failed to retrieve products." << endl;
    }

    cout << "\n1. Search for specific products\n";

    // 只有当用户已登录且有商品可选时，才显示添加到购物车的选项
    if (!currentUsername.empty() && productsLoaded) {
        cout << "2. Add product to cart\n";
        cout << "3. Return to " << "user menu" << "\n";
    } else {
        cout << "2. Return to " << (currentUsername.empty() ? "main menu" : "user menu") << "\n";
    }

    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    if (!currentUsername.empty() && productsLoaded) {
        switch (choice) {
            case 1:
                searchProductsScreen();
                break;
            case 2: {
                // 添加商品到购物车
                cout << "Enter the product number you want to add to cart: ";
                int productIndex;
                cin >> productIndex;
                cin.ignore();

                if (productIndex <= 0 || productIndex > static_cast<int>(productNames.size())) {
                    cout << "Invalid product number." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
                    displayProductsScreen();
                    return;
                }

                string productName = productNames[productIndex - 1];

                cout << "Please enter the quantity: ";
                int quantity;
                cin >> quantity;
                cin.ignore();

                // 发送添加到购物车请求
                string addRequest = "ADD_TO_CART " + productName + " " + to_string(quantity);
                cout << "Sending request: " << addRequest << endl;
                string addResponse = sendRequest(addRequest);

                if (addResponse == "CART_UPDATED") {
                    cout << "Item added to cart successfully!" << endl;
                } else {
                    cout << "Failed to add item to cart: " << addResponse << endl;
                }

                cout << "Press Enter to continue...";
                cin.get();
                displayProductsScreen();
                break;
            }
            case 3:
                return;
            default:
                cout << "Invalid choice. Returning to previous menu..." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                return;
        }
    } else {
        switch (choice) {
            case 1:
                searchProductsScreen();
                break;
            case 2:
                return;
            default:
                cout << "Invalid choice. Returning to previous menu..." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                return;
        }
    }
}

// 搜索商品界面
void searchProductsScreen() {
    system("cls");
    cout << "\n===== Search Products =====\n";

    cout << "Please enter the search keyword (or type 'back' to return): ";
    string keyword;
    getline(cin, keyword);

    if (keyword == "back") {
        return;
    }

    // 发送搜索商品请求
    string request = "GET_PRODUCTS " + keyword;
    string response = sendRequest(request);

    vector<string> productNames;
    bool productsFound = false;

    if (response.find("PRODUCTS") == 0) {
        // 解析并显示商品信息
        string productsData = response.substr(9); // 跳过"PRODUCTS "
        istringstream productsStream(productsData);
        string line;

        cout << "\nSearch results for '" << keyword << "':\n";
        int index = 1;

        // 跳过CSV文件头
        if (getline(productsStream, line)) {
            cout << "0. " << line << endl;
        }

        // 显示所有商品，并为每个商品添加索引
        while (getline(productsStream, line)) {
            cout << index << ". " << line << endl;

            // 提取商品名称 - 从格式化的输出中提取
            size_t nameStart = line.find("Name: ");
            size_t nameEnd = line.find(", Description:");

            if (nameStart != string::npos && nameEnd != string::npos) {
                // 提取名称，跳过"Name: "前缀
                string productName = line.substr(nameStart + 6, nameEnd - (nameStart + 6));
                productNames.push_back(productName);
            }

            index++;
        }

        productsFound = (index > 1);
    } else {
        cout << "Failed to retrieve products." << endl;
    }

    cout << "\n1. Search again\n";

    // 只有当用户已登录且找到商品时，才显示添加到购物车的选项
    if (!currentUsername.empty() && productsFound) {
        cout << "2. Add product to cart\n";
        cout << "3. Return to " << "user menu" << "\n";
    } else {
        cout << "2. Return to " << (currentUsername.empty() ? "main menu" : "user menu") << "\n";
    }

    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    if (!currentUsername.empty() && productsFound) {
        switch (choice) {
            case 1:
                searchProductsScreen();
                break;
            case 2: {
                // 添加商品到购物车
                cout << "Enter the product number you want to add to cart: ";
                int productIndex;
                cin >> productIndex;
                cin.ignore();

                if (productIndex <= 0 || productIndex > static_cast<int>(productNames.size())) {
                    cout << "Invalid product number." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
                    searchProductsScreen();
                    return;
                }

                string productName = productNames[productIndex - 1];

                cout << "Please enter the quantity: ";
                int quantity;
                cin >> quantity;
                cin.ignore();

                if (quantity <= 0) {
                    cout << "Quantity must be positive." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
                    searchProductsScreen();
                    return;
                }

                // 发送添加到购物车请求
                string addRequest = "ADD_TO_CART " + productName + " " + to_string(quantity);
                string addResponse = sendRequest(addRequest);

                if (addResponse == "CART_UPDATED") {
                    cout << "Item '" << productName << "' (quantity: " << quantity << ") added to cart successfully!" << endl;
                    cout << "\n1. Add another product\n";
                    cout << "2. Search again\n";
                    cout << "3. Return to previous menu\n";
                    cout << "Please enter your choice: ";

                    int nextChoice;
                    cin >> nextChoice;
                    cin.ignore();

                    switch (nextChoice) {
                        case 1:
                            // 继续添加商品 - 重新显示当前搜索结果
                            searchProductsScreen();
                            break;
                        case 2:
                            searchProductsScreen();
                            break;
                        case 3:
                            return;
                        default:
                            return;
                    }
                } else {
                    cout << "Failed to add item to cart: " << addResponse << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
                    searchProductsScreen();
                }
                break;
            }
            case 3:
                return;
            default:
                cout << "Invalid choice. Returning to previous menu..." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                return;
        }
    } else {
        switch (choice) {
            case 1:
                searchProductsScreen();
                break;
            case 2:
                return;
            default:
                cout << "Invalid choice. Returning to previous menu..." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                return;
        }
    }
}

// 购物车界面
void shoppingCartScreen() {
    system("cls");
    cout << "\n===== Shopping Cart =====\n";

    // 发送获取购物车请求
    string request = "GET_CART";
    string response = sendRequest(request);

    vector<vector<string>> cartItems;
    bool cartEmpty = false;

    if (response == "CART_EMPTY") {
        cout << "Your cart is empty." << endl;
        cartEmpty = true;
    } else if (response.find("CART") == 0) {
        // 解析并显示购物车信息
        string cartData = response.substr(5); // 跳过"CART "
        istringstream cartStream(cartData);
        string line;

        // 读取CSV文件头
        if (getline(cartStream, line)) {
            cartItems.push_back(split(line, ','));
        }

        // 读取所有购物车项目
        double totalAmount = 0;
        int itemIndex = 1;

        cout << "Your cart contains:\n";
        while (getline(cartStream, line)) {
            vector<string> itemData = split(line, ',');
            cartItems.push_back(itemData);

            if (itemData.size() >= 4) {
                string name = itemData[0];
                double price = stod(itemData[1]);
                int quantity = stoi(itemData[2]);
                string type = itemData[3];

                cout << itemIndex << ". " << name << " - Price: " << price
                     << " - Quantity: " << quantity << " - Type: " << type << endl;

                totalAmount += price * quantity;
                itemIndex++;
            }
        }

        if (itemIndex == 1) {
            cout << "Your cart is empty." << endl;
            cartEmpty = true;
        } else {
            cout << "\nTotal amount: " << totalAmount << endl;
        }
    } else {
        cout << "Failed to retrieve cart information." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        return;
    }

    cout << "\n1. Add item to cart\n";
    cout << "2. Remove item from cart\n";
    cout << "3. Modify item quantity\n";
    cout << "4. Generate order\n";
    cout << "5. Return to user menu\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    switch (choice) {
        case 1: {
            // 添加商品到购物车 - 直接调用显示商品界面
            displayProductsScreen();
            shoppingCartScreen();
            break;
        }
        case 2: {
            // 从购物车移除商品
            if (cartEmpty) {
                cout << "Your cart is empty." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen();
                return;
            }

            cout << "Enter the item number to remove: ";
            int itemIndex;
            cin >> itemIndex;
            cin.ignore();

            // 发送从购物车移除请求
            string removeRequest = "REMOVE_FROM_CART " + to_string(itemIndex);
            string removeResponse = sendRequest(removeRequest);

            if (removeResponse == "CART_UPDATED") {
                cout << "Item removed from cart successfully!" << endl;
            } else {
                cout << "Failed to remove item from cart: " << removeResponse << endl;
            }

            cout << "Press Enter to continue...";
            cin.get();
            shoppingCartScreen();
            break;
        }
        case 3: {
            // 修改购物车中商品数量
            if (cartEmpty) {
                cout << "Your cart is empty." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                shoppingCartScreen();
                return;
            }

            cout << "Enter the item number to modify: ";
            int itemIndex;
            cin >> itemIndex;
            cin.ignore();

            cout << "Enter new quantity: ";
            int newQuantity;
            cin >> newQuantity;
            cin.ignore();

            // 发送更新购物车数量请求
            string updateRequest = "UPDATE_CART_QUANTITY " + to_string(itemIndex) + " " + to_string(newQuantity);
            string updateResponse = sendRequest(updateRequest);

            if (updateResponse == "CART_UPDATED") {
                cout << "Item quantity updated successfully!" << endl;
            } else {
                cout << "Failed to update item quantity: " << updateResponse << endl;
            }

            cout << "Press Enter to continue...";
            cin.get();
            shoppingCartScreen();
            break;
        }
        case 4: {
            // 生成订单
            generateOrderScreen();
            break;
        }
        case 5: {
            // 返回用户菜单
            return;
        }
        default:
            cout << "Invalid choice. Returning to shopping cart..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            shoppingCartScreen();
    }
}

// 订单界面
void viewOrdersScreen() {
    system("cls");
    cout << "\n===== My Orders =====\n";

    // 发送获取订单请求
    string request = "GET_ORDERS";
    string response = sendRequest(request);

    vector<vector<string>> orders;
    bool hasOrders = false;

    if (response == "NO_ORDERS") {
        cout << "You have no orders." << endl;
    } else if (response.find("ORDERS") == 0) {
        // 解析并显示订单信息
        string ordersData = response.substr(7); // 跳过"ORDERS "
        istringstream ordersStream(ordersData);
        string line;

        // 读取CSV文件头
        if (getline(ordersStream, line)) {
            orders.push_back(split(line, ','));
        }

        // 读取所有订单
        int orderIndex = 1;
        cout << "Your orders:\n";

        while (getline(ordersStream, line)) {
            vector<string> orderData = split(line, ',');
            orders.push_back(orderData);

            if (orderData.size() >= 5) {
                cout << orderIndex << ". Order ID: " << orderData[0]
                     << " - Date: " << orderData[2]
                     << " - Status: " << orderData[3]
                     << " - Total: " << orderData[4] << endl;

                orderIndex++;
                hasOrders = true;
            }
        }

        if (!hasOrders) {
            cout << "You have no orders." << endl;
        }
    } else {
        cout << "Failed to retrieve orders." << endl;
    }

    if (!hasOrders) {
        cout << "\n1. Return to user menu\n";
        cout << "Please enter your choice: ";

        int choice;
        cin >> choice;
        cin.ignore();

        return;
    }

    cout << "\n1. Pay for an order\n";
    cout << "2. View order details\n";
    cout << "3. Return to user menu\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    switch (choice) {
        case 1: {
            // 支付订单
            cout << "Enter the order number to pay: ";
            int orderIndex;
            cin >> orderIndex;
            cin.ignore();

            if (orderIndex < 1 || orderIndex >= static_cast<int>(orders.size())) {
                cout << "Invalid order number." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen();
                return;
            }

            if (orders[orderIndex][3] != "unpaid") {
                cout << "This order is already " << orders[orderIndex][3] << "." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen();
                return;
            }

            payOrderScreen(orders[orderIndex][0]);
            break;
        }
        case 2: {
            // 查看订单详情
            cout << "Enter the order number to view: ";
            int orderIndex;
            cin >> orderIndex;
            cin.ignore();

            if (orderIndex < 1 || orderIndex >= static_cast<int>(orders.size())) {
                cout << "Invalid order number." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen();
                return;
            }

            string orderId = orders[orderIndex][0];

            // 发送获取订单详情请求
            string detailsRequest = "GET_ORDER_DETAILS " + orderId;
            string detailsResponse = sendRequest(detailsRequest);

            if (detailsResponse.find("ORDER_DETAILS") == 0) {
                // 解析并显示订单详情
                string detailsData = detailsResponse.substr(14); // 跳过"ORDER_DETAILS "
                istringstream detailsStream(detailsData);
                string line;

                cout << "\n===== Order Details =====\n";
                cout << "Order ID: " << orderId << endl;
                cout << "Date: " << orders[orderIndex][2] << endl;
                cout << "Status: " << orders[orderIndex][3] << endl;
                cout << "Total: " << orders[orderIndex][4] << endl;
                cout << "\nItems:\n";

                // 跳过CSV文件头
                if (getline(detailsStream, line)) {
                    // 文件头不处理
                }

                double total = 0;
                bool hasItems = false;

                while (getline(detailsStream, line)) {
                    vector<string> itemData = split(line, ',');
                    if (itemData.size() >= 3) {
                        hasItems = true;
                        string name = itemData[0];
                        double price = stod(itemData[1]);
                        int quantity = stoi(itemData[2]);
                        double itemTotal = price * quantity;
                        total += itemTotal;

                        cout << "- " << name << " (" << price << ") " << " x" << quantity
                             << " = " << itemTotal;

                        // 如果有商家信息，显示商家
                        if (itemData.size() >= 5 && !itemData[4].empty()) {
                            cout << " (Merchant: " << itemData[4] << ")";
                        }
                        cout << endl;
                    }
                }

                if (!hasItems) {
                    cout << "No items found in this order." << endl;
                }

                cout << "\nTotal: " << total << endl;
            } else {
                cout << "Failed to retrieve order details." << endl;
            }

            cout << "\nPress Enter to continue...";
            cin.get();
            viewOrdersScreen();
            break;
        }
        case 3:
            // 返回用户菜单
            return;
        default:
            cout << "Invalid choice. Returning to orders screen..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            viewOrdersScreen();
    }
}

// 生成订单界面
void generateOrderScreen() {
    system("cls");
    cout << "\n===== Generate Order =====\n";

    // 发送获取购物车请求
    string request = "GET_CART";
    string response = sendRequest(request);

    vector<vector<string>> cartItems;
    bool cartEmpty = false;

    if (response == "CART_EMPTY") {
        cout << "Your cart is empty." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen();
        return;
    } else if (response.find("CART") == 0) {
        // 解析并显示购物车信息
        string cartData = response.substr(5); // 跳过"CART "
        istringstream cartStream(cartData);
        string line;

        // 读取CSV文件头
        if (getline(cartStream, line)) {
            cartItems.push_back(split(line, ','));
        }

        // 读取所有购物车项目
        cout << "Your cart contains:\n";
        int itemIndex = 1;

        while (getline(cartStream, line)) {
            vector<string> itemData = split(line, ',');
            cartItems.push_back(itemData);

            if (itemData.size() >= 4) {
                cout << itemIndex << ". " << itemData[0] << " - Price: " << itemData[1]
                     << " - Quantity: " << itemData[2] << " - Type: " << itemData[3] << endl;
                itemIndex++;
            }
        }

        if (itemIndex == 1) {
            cout << "Your cart is empty." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            shoppingCartScreen();
            return;
        }
    } else {
        cout << "Failed to retrieve cart information." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen();
        return;
    }

    // 选择要生成订单的商品
    cout << "\nSelect items to include in the order (enter item numbers separated by spaces, 'all' for all items, or 'back' to return): ";
    string selection;
    getline(cin, selection);

    // 检查是否要返回
    if (selection == "back") {
        cout << "Returning to shopping cart..." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen();
        return;
    }

    string selectedItemsStr;
    if (selection == "all") {
        // 选择所有商品
        for (int i = 1; i < cartItems.size(); i++) {
            selectedItemsStr += to_string(i) + " ";
        }
    } else {
        selectedItemsStr = selection;
    }

    // 发送生成订单请求
    string generateRequest = "GENERATE_ORDER " + selectedItemsStr;
    string generateResponse = sendRequest(generateRequest);

    if (generateResponse.find("ORDER_GENERATED") == 0) {
        // 解析订单ID和总金额
        vector<string> parts = split(generateResponse, ' ');
        if (parts.size() >= 3) {
            string orderId = parts[1];
            double totalAmount = stod(parts[2]);

            cout << "Order generated successfully!" << endl;
            cout << "Order ID: " << orderId << endl;
            cout << "Total Amount: " << totalAmount << endl;

            // 询问是否立即支付
            cout << "Would you like to pay for this order now? (y/n): ";
            char confirm;
            cin >> confirm;
            cin.ignore();

            if (tolower(confirm) == 'y') {
                payOrderScreen(orderId);
            } else {
                viewOrdersScreen();
            }
        }
    } else {
        cout << "Failed to generate order: " << generateResponse << endl;
        cout << "Press Enter to continue...";
        cin.get();
        shoppingCartScreen();
    }
}

// 支付订单界面
void payOrderScreen(const string& orderId) {
    system("cls");
    cout << "\n===== Pay Order =====\n";
    cout << "Order ID: " << orderId << endl;

    // 首先获取订单金额
    string orderDetailsRequest = "GET_ORDER_DETAILS " + orderId;
    string orderDetailsResponse = sendRequest(orderDetailsRequest);

    double totalAmount = 0.0;

    // 解析订单详情，计算总金额
    if (orderDetailsResponse.find("ORDER_DETAILS") == 0) {
        string detailsData = orderDetailsResponse.substr(14); // 跳过"ORDER_DETAILS "
        istringstream detailsStream(detailsData);
        string line;

        // 跳过CSV文件头
        if (getline(detailsStream, line)) {
            // 文件头不处理
        }

        // 计算总金额
        while (getline(detailsStream, line)) {
            vector<string> itemData = split(line, ',');
            if (itemData.size() >= 3) {
                double price = stod(itemData[1]);
                int quantity = stoi(itemData[2]);
                totalAmount += price * quantity;
            }
        }
    }

    // 获取当前余额
    string balanceRequest = "GET_BALANCE";
    string balanceResponse = sendRequest(balanceRequest);
    double currentBalance = 0.0;

    if (balanceResponse.find("BALANCE") == 0) {
        string balanceStr = balanceResponse.substr(8); // 跳过"BALANCE "
        currentBalance = stod(balanceStr);
        cout << "Your current balance: " << currentBalance << endl;

        // 检查余额是否足够
        if (currentBalance < totalAmount) {
            cout << "Insufficient balance. Required: " << totalAmount << endl;
            cout << "\n1. Recharge your account\n";
            cout << "2. Return to orders screen\n";
            cout << "Please enter your choice: ";

            int choice;
            cin >> choice;
            cin.ignore();

            switch (choice) {
                case 1:
                    rechargeScreen();
                    // 充值后再次尝试支付
                    payOrderScreen(orderId);
                    return;
                case 2:
                    viewOrdersScreen();
                    return;
                default:
                    cout << "Invalid choice. Returning to orders screen..." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
                    viewOrdersScreen();
                    return;
            }
        }
    }

    // 获取密码
    cout << "Please enter your password to confirm payment: ";
    string password;
    getline(cin, password);

    cout << "Processing payment, please wait..." << endl;

    // 发送支付订单请求
    string payRequest = "PAY_ORDER " + orderId + " " + password;
    string payResponse = sendRequest(payRequest);

    // 处理网络错误
    if (payResponse == "ERROR_TIMEOUT") {
        cout << "Payment request timed out. The server may be busy." << endl;
        cout << "\n1. Try again\n";
        cout << "2. Return to orders screen\n";
        cout << "Please enter your choice: ";

        int choice;
        cin >> choice;
        cin.ignore();

        switch (choice) {
            case 1:
                payOrderScreen(orderId);
                return;
            case 2:
                viewOrdersScreen();
                return;
            default:
                cout << "Invalid choice. Returning to orders screen..." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen();
                return;
        }
    }

    // 处理支付超时，可能是余额不足
    if (payResponse == "ERROR_PAYMENT_TIMEOUT") {
        cout << "Payment processing timed out. This may be due to insufficient balance." << endl;

        // 获取当前余额
        string balanceRequest = "GET_BALANCE";
        string balanceResponse = sendRequest(balanceRequest);

        if (balanceResponse.find("BALANCE") == 0) {
            // 解析余额
            string balanceStr = balanceResponse.substr(8); // 跳过"BALANCE "
            double balance = stod(balanceStr);
            cout << "Your current balance: " << balance << endl;
        }

        cout << "\n1. Recharge your account\n";
        cout << "2. Try payment again\n";
        cout << "3. Return to orders screen\n";
        cout << "Please enter your choice: ";

        int choice;
        cin >> choice;
        cin.ignore();

        switch (choice) {
            case 1:
                rechargeScreen();
                // 充值后再次尝试支付
                payOrderScreen(orderId);
                return;
            case 2:
                payOrderScreen(orderId);
                return;
            case 3:
                viewOrdersScreen();
                return;
            default:
                cout << "Invalid choice. Returning to orders screen..." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen();
                return;
        }
    }

    if (payResponse == "ERROR_SEND_FAILED" || payResponse == "ERROR_RECEIVE_FAILED") {
        cout << "Network error occurred. Please check your connection." << endl;
        cout << "Press Enter to return to orders screen...";
        cin.get();
        viewOrdersScreen();
        return;
    }

    // 处理支付成功
    if (payResponse == "PAYMENT_SUCCESS") {
        cout << "Payment successful! Thank you for your purchase." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen();
        return;
    }

    // 处理支付失败的情况
    cout << "Payment failed: " << payResponse << endl;

    // 检查是否是余额不足的错误
    if (payResponse.find("ERROR Insufficient balance") != string::npos) {
        cout << "\n1. Recharge your account\n";
        cout << "2. Return to orders screen\n";
        cout << "Please enter your choice: ";

        int choice;
        cin >> choice;
        cin.ignore();

        switch (choice) {
            case 1:
                rechargeScreen();
                // 充值后再次尝试支付
                payOrderScreen(orderId);
                return;
            case 2:
                viewOrdersScreen();
                return;
            default:
                cout << "Invalid choice. Returning to orders screen..." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewOrdersScreen();
                return;
        }
    } else {
        // 其他错误情况
        cout << "Press Enter to continue...";
        cin.get();
        viewOrdersScreen();
    }
}

// 查询余额界面
void checkBalanceScreen() {
    system("cls");
    cout << "\n===== Check Balance =====\n";

    // 发送获取余额请求
    string request = "GET_BALANCE";
    string response = sendRequest(request);

    if (response.find("BALANCE") == 0) {
        // 解析余额
        string balanceStr = response.substr(8); // 跳过"BALANCE "
        double balance = stod(balanceStr);
        cout << "Current balance: " << balance << endl;
    } else {
        cout << "Failed to retrieve balance information." << endl;
    }

    cout << "\n1. Recharge\n";
    cout << "2. Return to user menu\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    switch (choice) {
        case 1:
            rechargeScreen();
            break;
        case 2:
            return;
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            return;
    }
}

// 充值界面
void rechargeScreen() {
    system("cls");
    cout << "\n===== Recharge =====\n";

    // 显示当前余额
    string balanceRequest = "GET_BALANCE";
    string balanceResponse = sendRequest(balanceRequest);

    if (balanceResponse.find("BALANCE") == 0) {
        // 解析余额
        string balanceStr = balanceResponse.substr(8); // 跳过"BALANCE "
        double balance = stod(balanceStr);
        cout << "Current balance: " << balance << endl;
    }

    cout << "Please enter the recharge amount: ";
    double amount;
    cin >> amount;
    cin.ignore();

    if (amount <= 0) {
        cout << "Invalid amount. Please enter a positive number." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        rechargeScreen();
        return;
    }

    // 发送充值请求
    string rechargeRequest = "RECHARGE " + to_string(amount);
    string rechargeResponse = sendRequest(rechargeRequest);

    if (rechargeResponse.find("RECHARGE_SUCCESS") == 0) {
        // 解析新余额
        string newBalanceStr = rechargeResponse.substr(17); // 跳过"RECHARGE_SUCCESS "
        double newBalance = stod(newBalanceStr);
        cout << "Recharge successful!" << endl;
        cout << "New balance: " << newBalance << endl;
    } else {
        cout << "Recharge failed: " << rechargeResponse << endl;
    }

    cout << "\n1. Recharge again\n";
    cout << "2. Return to user menu\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    switch (choice) {
        case 1:
            rechargeScreen();
            break;
        case 2:
            return;
        default:
            cout << "Invalid choice. Returning to user menu..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            return;
    }
}

// 修改密码界面
void changePasswordScreen() {
    system("cls");
    cout << "\n===== Change Password =====\n";

    string oldPassword, newPassword;

    cout << "Please enter your current password: ";
    getline(cin, oldPassword);

    cout << "Please enter your new password (at least 6 characters, must include letters, digits, and symbols): ";
    getline(cin, newPassword);

    // 发送修改密码请求
    string changeRequest = "CHANGE_PASSWORD " + oldPassword + " " + newPassword;
    string changeResponse = sendRequest(changeRequest);

    if (changeResponse == "PASSWORD_CHANGED") {
        cout << "Password changed successfully!" << endl;
    } else {
        cout << "Failed to change password: " << changeResponse << endl;
    }

    cout << "Press Enter to continue...";
    cin.get();
}



// 查看商家订单详情
void viewMerchantOrderDetailsScreen(const string& orderId) {
    system("cls");
    cout << "\n===== Order Details =====\n";
    cout << "Order ID: " << orderId << endl;

    // 发送获取订单详情请求
    string request = "GET_ORDER_DETAILS " + orderId;
    string response = sendRequest(request);

    if (response.find("ORDER_DETAILS") == 0) {
        // 解析并显示订单详情
        string detailsData = response.substr(14); // 跳过"ORDER_DETAILS "
        istringstream detailsStream(detailsData);
        string line;

        cout << "\nOrder Items:\n";
        cout << "Item | Price | Quantity | Total | Merchant\n";
        cout << "--------------------------------------------\n";

        // 跳过CSV文件头
        if (getline(detailsStream, line)) {
            // 文件头不处理
        }

        double totalAmount = 0;
        int itemCount = 0;

        while (getline(detailsStream, line)) {
            vector<string> itemData = split(line, ',');
            if (itemData.size() >= 3) {
                string name = itemData[0];
                double price = stod(itemData[1]);
                int quantity = stoi(itemData[2]);
                double itemTotal = price * quantity;
                totalAmount += itemTotal;
                itemCount++;

                cout << name << " | $" << price << " | " << quantity
                     << " | $" << itemTotal;

                // 如果有商家信息，显示商家
                if (itemData.size() >= 5 && !itemData[4].empty()) {
                    cout << " | " << itemData[4];
                }
                cout << endl;
            }
        }

        if (itemCount == 0) {
            cout << "No items found in this order." << endl;
        } else {
            cout << "\nTotal Items: " << itemCount << endl;
            cout << "Total Amount: $" << totalAmount << endl;
        }
    } else {
        cout << "Failed to retrieve order details: " << response << endl;
    }

    cout << "\n1. Return to orders list\n";
    cout << "2. Return to user menu\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    switch (choice) {
        case 1:
            viewReceivedOrdersScreen();
            break;
        case 2:
            return;
        default:
            cout << "Invalid choice. Returning to orders list..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            viewReceivedOrdersScreen();
    }
}

// 获取订单支付状态
string getOrderPaymentStatus(const string& orderId) {
    // 发送获取订单状态请求
    string request = "GET_ORDER_STATUS " + orderId;
    string response = sendRequest(request);

    if (response.find("ORDER_STATUS") == 0) {
        // 解析状态信息
        string statusData = response.substr(13); // 跳过"ORDER_STATUS "
        return statusData;
    }

    return "unknown";
}

// 查看收入统计界面
void viewRevenueStatisticsScreen() {
    system("cls");
    cout << "\n===== Revenue Statistics =====\n";

    // 获取当前余额
    string balanceRequest = "GET_BALANCE";
    string balanceResponse = sendRequest(balanceRequest);

    double currentBalance = 0.0;
    if (balanceResponse.find("BALANCE") == 0) {
        string balanceStr = balanceResponse.substr(8); // 跳过"BALANCE "
        currentBalance = stod(balanceStr);
    }

    // 获取商家订单信息
    string request = "GET_MERCHANT_ORDERS";
    string response = sendRequest(request);

    double totalRevenue = 0.0;
    double pendingRevenue = 0.0;
    int totalOrders = 0;
    int paidOrders = 0;
    int unpaidOrders = 0;

    if (response.find("ORDERS") == 0) {
        string ordersData = response.substr(7); // 跳过"ORDERS "
        istringstream ordersStream(ordersData);
        string line;

        // 跳过CSV文件头
        if (getline(ordersStream, line)) {
            // 文件头不处理
        }

        // 统计所有订单
        while (getline(ordersStream, line)) {
            vector<string> orderData = split(line, ',');
            if (orderData.size() >= 4) {
                string orderId = orderData[0];
                double amount = stod(orderData[3]);
                string paymentStatus = getOrderPaymentStatus(orderId);

                totalOrders++;
                if (paymentStatus == "paid") {
                    totalRevenue += amount;
                    paidOrders++;
                } else {
                    pendingRevenue += amount;
                    unpaidOrders++;
                }
            }
        }
    }

    cout << "Current Account Balance: $" << currentBalance << endl;
    cout << "\n===== Order Statistics =====\n";
    cout << "Total Orders Received: " << totalOrders << endl;
    cout << "Paid Orders: " << paidOrders << endl;
    cout << "Unpaid Orders: " << unpaidOrders << endl;

    cout << "\n===== Revenue Statistics =====\n";
    cout << "Total Revenue Received: $" << totalRevenue << endl;
    cout << "Pending Revenue: $" << pendingRevenue << endl;
    cout << "Total Expected Revenue: $" << (totalRevenue + pendingRevenue) << endl;

    if (totalOrders > 0) {
        double averageOrderValue = (totalRevenue + pendingRevenue) / totalOrders;
        double paymentRate = (double)paidOrders / totalOrders * 100;
        cout << "Average Order Value: $" << averageOrderValue << endl;
        cout << "Payment Rate: " << paymentRate << "%" << endl;
    }

    cout << "\n===== Account Information =====\n";
    cout << "Funds Available for Withdrawal: $" << currentBalance << endl;

    cout << "\n1. Return to orders list\n";
    cout << "2. Return to user menu\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    switch (choice) {
        case 1:
            viewReceivedOrdersScreen();
            break;
        case 2:
            return;
        default:
            cout << "Invalid choice. Returning to orders list..." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            viewReceivedOrdersScreen();
    }
}

// 商家管理商品界面
void manageProductsScreen() {
    system("cls");
    cout << "\n===== Product Management =====\n";
    cout << "Welcome, " << currentUsername << "!\n\n";

    cout << "1. View My Products\n";
    cout << "2. Add New Product\n";
    cout << "3. Update Product Price\n";
    cout << "4. Update Product Quantity\n";
    cout << "5. Manage Category Discounts\n";
    cout << "6. Return to User Menu\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    switch (choice) {
        case 1:
            viewMyProductsScreen();
            break;
        case 2:
            addProductScreen();
            break;
        case 3:
            updateProductPriceScreen();
            break;
        case 4:
            updateProductQuantityScreen();
            break;
        case 5:
            manageCategoryDiscountsScreen();
            break;
        case 6:
            return;
        default:
            cout << "Invalid choice. Please try again." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            manageProductsScreen();
    }
}

// 查看商家自己的商品
void viewMyProductsScreen() {
    system("cls");
    cout << "\n===== My Products =====\n";

    // 发送获取商家自己商品的请求
    string request = "GET_MERCHANT_PRODUCTS";
    string response = sendRequest(request);

    if (response.find("PRODUCTS") == 0) {
        // 解析并显示商品信息
        string productsData = response.substr(9); // 跳过"PRODUCTS "
        istringstream productsStream(productsData);
        string line;

        // 跳过CSV文件头并显示格式化的表头
        if (getline(productsStream, line)) {
            cout << "Product List:\n";
            cout << "No. | Name | Description | Price | Quantity | Type | Merchant\n";
            cout << "------------------------------------------------------------\n";
        }

        // 显示所有商品
        int count = 0;
        while (getline(productsStream, line)) {
            vector<string> productData = split(line, ',');
            if (productData.size() >= 6) {
                cout << (count + 1) << ". " << productData[0] << " | " << productData[1]
                     << " | $" << productData[2] << " | " << productData[3]
                     << " | " << productData[4] << " | " << productData[5] << endl;
                count++;
            }
        }

        if (count == 0) {
            cout << "You haven't added any products yet." << endl;
        } else {
            cout << "\nTotal products: " << count << endl;
        }
    } else {
        cout << "Failed to retrieve product information: " << response << endl;
    }

    cout << "\nPress Enter to return to product management menu...";
    cin.get();
    manageProductsScreen();
}

// 添加新商品
void addProductScreen() {
    system("cls");
    cout << "\n===== Add New Product =====\n";

    string name, description, type;
    double price;
    int quantity;

    cout << "Please enter product name: ";
    getline(cin, name);

    if (name.empty()) {
        cout << "Product name cannot be empty." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    cout << "Please enter product description: ";
    getline(cin, description);

    if (description.empty()) {
        cout << "Product description cannot be empty." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    cout << "Please enter product price: $";
    cin >> price;
    cin.ignore();

    if (price <= 0) {
        cout << "Price must be positive." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    cout << "Please enter product quantity: ";
    cin >> quantity;
    cin.ignore();

    if (quantity < 0) {
        cout << "Quantity cannot be negative." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    cout << "Please enter product type:\n";
    cout << "Available types: Book, Clothing, Food, DailyNecessities, PersonalCare, Electronics, Beverages, MaternityAndBabyProducts, Others\n";
    cout << "Type: ";
    getline(cin, type);

    if (type.empty()) {
        cout << "Product type cannot be empty." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    // 发送添加商品请求 - 使用引号包围可能包含空格的参数
    string request = "ADD_PRODUCT \"" + name + "\" \"" + description + "\" " + to_string(price) + " " + to_string(quantity) + " " + type;
    string response = sendRequest(request);

    if (response == "PRODUCT_ADDED") {
        cout << "Product added successfully!" << endl;
    } else {
        cout << "Failed to add product: " << response << endl;
    }

    cout << "Press Enter to return to product management menu...";
    cin.get();
    manageProductsScreen();
}

// 更新商品价格
void updateProductPriceScreen() {
    system("cls");
    cout << "\n===== Update Product Price =====\n";

    // 先获取商家的商品列表
    string request = "GET_MERCHANT_PRODUCTS";
    string response = sendRequest(request);

    vector<string> productNames;
    bool hasProducts = false;

    if (response.find("PRODUCTS") == 0) {
        // 解析并显示商品信息
        string productsData = response.substr(9); // 跳过"PRODUCTS "
        istringstream productsStream(productsData);
        string line;

        // 跳过CSV文件头并显示格式化的表头
        if (getline(productsStream, line)) {
            cout << "Your Products:\n";
            cout << "No. | Name | Description | Current Price | Quantity | Type\n";
            cout << "--------------------------------------------------------\n";
        }

        // 显示所有商品，并为每个商品添加索引
        int index = 1;
        while (getline(productsStream, line)) {
            vector<string> productData = split(line, ',');
            if (productData.size() >= 6) {
                cout << index << ". " << productData[0] << " | " << productData[1]
                     << " | $" << productData[2] << " | " << productData[3]
                     << " | " << productData[4] << endl;
                productNames.push_back(productData[0]);
                hasProducts = true;
                index++;
            }
        }
    }

    if (!hasProducts) {
        cout << "You haven't added any products yet." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    cout << "\nPlease enter the product number to update price (0 to cancel): ";
    int productIndex;
    cin >> productIndex;
    cin.ignore();

    if (productIndex == 0) {
        manageProductsScreen();
        return;
    }

    if (productIndex <= 0 || productIndex > static_cast<int>(productNames.size())) {
        cout << "Invalid product number." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    string productName = productNames[productIndex - 1];

    cout << "Please enter new price: $";
    double newPrice;
    cin >> newPrice;
    cin.ignore();

    if (newPrice <= 0) {
        cout << "Price must be positive." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    // 发送更新价格请求 - 使用引号包围商品名称
    string updateRequest = "UPDATE_PRODUCT_PRICE \"" + productName + "\" " + to_string(newPrice);
    string updateResponse = sendRequest(updateRequest);

    if (updateResponse == "PRODUCT_UPDATED") {
        cout << "Product price updated successfully!" << endl;
        cout << "Product: " << productName << " - New price: $" << newPrice << endl;
    } else {
        cout << "Failed to update product price: " << updateResponse << endl;
    }

    cout << "Press Enter to return to product management menu...";
    cin.get();
    manageProductsScreen();
}

// 更新商品库存
void updateProductQuantityScreen() {
    system("cls");
    cout << "\n===== Update Product Quantity =====\n";

    // 先获取商家的商品列表
    string request = "GET_MERCHANT_PRODUCTS";
    string response = sendRequest(request);

    vector<string> productNames;
    bool hasProducts = false;

    if (response.find("PRODUCTS") == 0) {
        // 解析并显示商品信息
        string productsData = response.substr(9); // 跳过"PRODUCTS "
        istringstream productsStream(productsData);
        string line;

        // 跳过CSV文件头并显示格式化的表头
        if (getline(productsStream, line)) {
            cout << "Your Products:\n";
            cout << "No. | Name | Description | Price | Current Quantity | Type\n";
            cout << "--------------------------------------------------------\n";
        }

        // 显示所有商品，并为每个商品添加索引
        int index = 1;
        while (getline(productsStream, line)) {
            vector<string> productData = split(line, ',');
            if (productData.size() >= 6) {
                cout << index << ". " << productData[0] << " | " << productData[1]
                     << " | $" << productData[2] << " | " << productData[3]
                     << " | " << productData[4] << endl;
                productNames.push_back(productData[0]);
                hasProducts = true;
                index++;
            }
        }
    }

    if (!hasProducts) {
        cout << "You haven't added any products yet." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    cout << "\nPlease enter the product number to update quantity (0 to cancel): ";
    int productIndex;
    cin >> productIndex;
    cin.ignore();

    if (productIndex == 0) {
        manageProductsScreen();
        return;
    }

    if (productIndex <= 0 || productIndex > static_cast<int>(productNames.size())) {
        cout << "Invalid product number." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    string productName = productNames[productIndex - 1];

    cout << "Please enter new quantity: ";
    int newQuantity;
    cin >> newQuantity;
    cin.ignore();

    if (newQuantity < 0) {
        cout << "Quantity cannot be negative." << endl;
        cout << "Press Enter to return to product management menu...";
        cin.get();
        manageProductsScreen();
        return;
    }

    // 发送更新库存请求 - 使用引号包围商品名称
    string updateRequest = "UPDATE_PRODUCT_QUANTITY \"" + productName + "\" " + to_string(newQuantity);
    string updateResponse = sendRequest(updateRequest);

    if (updateResponse == "PRODUCT_UPDATED") {
        cout << "Product quantity updated successfully!" << endl;
        cout << "Product: " << productName << " - New quantity: " << newQuantity << endl;
    } else {
        cout << "Failed to update product quantity: " << updateResponse << endl;
    }

    cout << "Press Enter to return to product management menu...";
    cin.get();
    manageProductsScreen();
}

// 查看商家收到的订单
void viewReceivedOrdersScreen() {
    system("cls");
    cout << "\n===== Received Orders & Payment Status =====\n";

    // 首先刷新并显示当前余额
    cout << "Refreshing account information..." << endl;
    string balanceRequest = "GET_BALANCE";
    string balanceResponse = sendRequest(balanceRequest);
    double currentBalance = 0.0;
    if (balanceResponse.find("BALANCE") == 0) {
        string balanceStr = balanceResponse.substr(8); // 跳过"BALANCE "
        currentBalance = stod(balanceStr);
    }
    cout << "Current Account Balance: $" << currentBalance << endl;
    cout << "Checking for new payments..." << endl << endl;

    // 发送获取商家订单请求
    string request = "GET_MERCHANT_ORDERS";
    string response = sendRequest(request);

    vector<vector<string>> orders;
    bool hasOrders = false;
    double totalPaidAmount = 0.0;
    double totalUnpaidAmount = 0.0;
    int paidCount = 0;
    int unpaidCount = 0;

    if (response == "NO_ORDERS") {
        cout << "You haven't received any orders yet." << endl;
    } else if (response.find("ORDERS") == 0) {
        // 解析并显示订单信息
        string ordersData = response.substr(7); // 跳过"ORDERS "
        istringstream ordersStream(ordersData);
        string line;

        // 读取CSV文件头
        if (getline(ordersStream, line)) {
            orders.push_back(split(line, ','));
            cout << "Order List:\n";
            cout << "No. | Order ID | Customer | Date | Amount | Payment Status\n";
            cout << "----------------------------------------------------------------\n";
        }

        // 读取所有订单
        int orderIndex = 1;

        while (getline(ordersStream, line)) {
            vector<string> orderData = split(line, ',');
            orders.push_back(orderData);

            if (orderData.size() >= 4) {
                // 获取订单支付状态
                string orderId = orderData[0];
                string paymentStatus = getOrderPaymentStatus(orderId);
                double amount = stod(orderData[3]);

                cout << orderIndex << ". " << orderData[0] << " | "
                     << orderData[1] << " | " << orderData[2] << " | $"
                     << orderData[3] << " | ";

                if (paymentStatus == "paid") {
                    cout << "PAID" << endl;
                    totalPaidAmount += amount;
                    paidCount++;
                } else {
                    cout << "UNPAID" << endl;
                    totalUnpaidAmount += amount;
                    unpaidCount++;
                }

                orderIndex++;
                hasOrders = true;
            }
        }

        if (hasOrders) {
            cout << "\n===== Payment Summary =====\n";
            cout << "Total Orders: " << (orderIndex - 1) << endl;
            cout << "Paid Orders: " << paidCount << " (Total: $" << totalPaidAmount << ")" << endl;
            cout << "Unpaid Orders: " << unpaidCount << " (Total: $" << totalUnpaidAmount << ")" << endl;
            cout << "Total Revenue Received: $" << totalPaidAmount << endl;
            cout << "Pending Revenue: $" << totalUnpaidAmount << endl;

            cout << "\n1. View order details\n";
            cout << "2. View revenue statistics\n";
            cout << "3. Refresh orders and balance\n";
            cout << "4. Return to user menu\n";
            cout << "Please enter your choice: ";

            int choice;
            cin >> choice;
            cin.ignore();

            if (choice == 1) {
                cout << "Please enter the order number to view details (0 to cancel): ";
                int selectedOrder;
                cin >> selectedOrder;
                cin.ignore();

                if (selectedOrder == 0) {
                    viewReceivedOrdersScreen();
                    return;
                }

                if (selectedOrder <= 0 || selectedOrder >= orderIndex) {
                    cout << "Invalid order number." << endl;
                    cout << "Press Enter to continue...";
                    cin.get();
                    viewReceivedOrdersScreen();
                    return;
                } else {
                    // 显示订单详情
                    string orderId = orders[selectedOrder][0];
                    viewMerchantOrderDetailsScreen(orderId);
                    return;
                }
            } else if (choice == 2) {
                viewRevenueStatisticsScreen();
                return;
            } else if (choice == 3) {
                // 刷新订单和余额
                cout << "Refreshing data..." << endl;
                viewReceivedOrdersScreen();
                return;
            } else if (choice == 4) {
                return;
            } else {
                cout << "Invalid choice. Returning to orders screen..." << endl;
                cout << "Press Enter to continue...";
                cin.get();
                viewReceivedOrdersScreen();
                return;
            }
        }
    } else {
        cout << "Failed to retrieve order information: " << response << endl;
    }

    cout << "Press Enter to return to user menu...";
    cin.get();
}

// 主函数
int main() {
    // 初始化Winsock
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        cerr << "WSAStartup failed: " << result << endl;
        return 1;
    }

    // 创建套接字
    clientSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (clientSocket == INVALID_SOCKET) {
        cerr << "Socket creation failed: " << WSAGetLastError() << endl;
        WSACleanup();
        return 1;
    }

    // 连接服务器
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(PORT);
    inet_pton(AF_INET, SERVER_IP.c_str(), &serverAddr.sin_addr);

    result = connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr));
    if (result == SOCKET_ERROR) {
        cerr << "Connection failed: " << WSAGetLastError() << endl;
        closesocket(clientSocket);
        WSACleanup();
        return 1;
    }

    cout << "Connected to server at " << SERVER_IP << ":" << PORT << endl;

    // 显示主菜单
    mainMenuScreen();

    // 断开连接
    string disconnectRequest = "DISCONNECT";
    sendRequest(disconnectRequest);

    // 清理资源
    closesocket(clientSocket);
    WSACleanup();

    return 0;
}

// 主菜单界面
void mainMenuScreen() {
    while (true) {
        system("cls");
        cout << "\n===== E-Commerce Platform =====\n";
        cout << "1. Register\n";
        cout << "2. Login\n";
        cout << "3. Display Products\n";
        cout << "4. Search Products\n";
        cout << "5. Exit\n";
        cout << "Please enter your choice: ";

        int choice;
        cin >> choice;
        cin.ignore();

        switch (choice) {
            case 1:
                registerScreen();
                break;
            case 2:
                loginScreen();
                break;
            case 3:
                displayProductsScreen();
                break;
            case 4:
                searchProductsScreen();
                break;
            case 5:
                cout << "Thank you for using our platform. Goodbye!" << endl;
                return;
            default:
                cout << "Invalid choice. Please try again." << endl;
                cout << "Press Enter to continue...";
                cin.get();
        }
    }
}

// 登录界面
void loginScreen() {
    system("cls");
    cout << "\n===== Login =====\n";

    string username, password;
    bool usernameValid = false;

    // 获取并验证用户名
    while (!usernameValid) {
        cout << "Please enter your username: ";
        getline(cin, username);

        // 检查用户名是否存在
        string checkRequest = "CHECK_USERNAME " + username;
        string response = sendRequest(checkRequest);

        if (response == "USERNAME_EXISTS") {
            usernameValid = true;
        } else {
            cout << "Username not found. Would you like to:" << endl;
            cout << "1. Try another username" << endl;
            cout << "2. Go to registration page" << endl;
            cout << "Please choose: ";

            int choice;
            cin >> choice;
            cin.ignore();

            switch (choice) {
                case 1:
                    // 继续循环，重新输入用户名
                    break;
                case 2:
                    // 跳转到注册界面
                    registerScreen();
                    return;
                default:
                    cout << "Invalid choice, defaulting to try another username." << endl;
                    break;
            }
        }
    }

    // 获取密码
    cout << "Please enter your password: ";
    getline(cin, password);

    // 发送登录请求
    string loginRequest = "LOGIN " + username + " " + password;
    string response = sendRequest(loginRequest);

    if (response.find("LOGIN_SUCCESS") == 0) {
        // 登录成功，解析用户类型和用户名
        vector<string> parts = split(response, ' ');
        if (parts.size() >= 3) {
            userType = parts[1];
            currentUsername = parts[2];
            cout << "Login successful! Welcome, " << currentUsername << "!" << endl;
            cout << "Press Enter to continue...";
            cin.get();
            userMenuScreen();
        }
    } else {
        cout << "Login failed. Incorrect password." << endl;
        cout << "Press Enter to continue...";
        cin.get();
    }
}

// 注册界面
void registerScreen() {
    system("cls");
    cout << "\n===== Register =====\n";

    string username, password, type;

    // 获取用户名并检查是否已存在
    while (true) {
        cout << "Please enter your username: ";
        getline(cin, username);

        // 检查用户名是否存在
        string checkRequest = "CHECK_USERNAME " + username;
        string response = sendRequest(checkRequest);

        if (response == "USERNAME_EXISTS") {
            cout << "This username already exists. Would you like to:" << endl;
            cout << "1. Try another username" << endl;
            cout << "2. Go to login page" << endl;
            cout << "3. Return to main menu" << endl;
            cout << "Please choose: ";

            int choice;
            cin >> choice;
            cin.ignore();

            switch (choice) {
                case 1:
                    // 继续循环，重新输入用户名
                    break;
                case 2:
                    // 跳转到登录界面
                    loginScreen();
                    return;
                case 3:
                    // 返回主界面
                    return;
                default:
                    cout << "Invalid choice, defaulting to try another username." << endl;
                    break;
            }
        } else {
            break;
        }
    }

    // 获取密码
    cout << "Please enter your password (at least 6 characters, must include letters, digits, and symbols): ";
    getline(cin, password);

    // 获取用户类型
    while (true) {
        cout << "Please enter your user type (consumer/merchant): ";
        getline(cin, type);

        if (type != "consumer" && type != "merchant") {
            cout << "Invalid user type. Please enter 'consumer' or 'merchant'." << endl;
        } else {
            break;
        }
    }

    // 发送注册请求
    string registerRequest = "REGISTER " + username + " " + password + " " + type;
    string response = sendRequest(registerRequest);

    if (response == "REGISTER_SUCCESS") {
        cout << "Registration successful!" << endl;
        cout << "Press Enter to continue...";
        cin.get();

        // 自动登录
        string loginRequest = "LOGIN " + username + " " + password;
        response = sendRequest(loginRequest);

        if (response.find("LOGIN_SUCCESS") == 0) {
            // 登录成功，解析用户类型和用户名
            vector<string> parts = split(response, ' ');
            if (parts.size() >= 3) {
                userType = parts[1];
                currentUsername = parts[2];
                userMenuScreen();
            }
        }
    } else {
        cout << "Registration failed: " << response << endl;
        cout << "Press Enter to continue...";
        cin.get();
    }
}

// 管理商品类别折扣界面
void manageCategoryDiscountsScreen() {
    system("cls");
    cout << "\n===== Manage Category Discounts =====\n";
    cout << "Welcome, " << currentUsername << "!\n\n";

    cout << "1. View Current Discounts\n";
    cout << "2. Set Category Discount\n";
    cout << "3. Return to Product Management\n";
    cout << "Please enter your choice: ";

    int choice;
    cin >> choice;
    cin.ignore();

    switch (choice) {
        case 1:
            viewDiscountsScreen();
            break;
        case 2:
            setDiscountScreen();
            break;
        case 3:
            return;
        default:
            cout << "Invalid choice. Please try again." << endl;
            cout << "Press Enter to continue...";
            cin.get();
            manageCategoryDiscountsScreen();
    }
}

// 查看当前折扣界面
void viewDiscountsScreen() {
    system("cls");
    cout << "\n===== Current Category Discounts =====\n";

    // 发送获取折扣信息请求
    string request = "GET_DISCOUNTS";
    string response = sendRequest(request);

    if (response.find("DISCOUNTS") == 0) {
        // 解析并显示折扣信息
        string discountsData = response.substr(10); // 跳过"DISCOUNTS "
        istringstream discountsStream(discountsData);
        string line;

        // 跳过CSV文件头并显示格式化的表头
        if (getline(discountsStream, line)) {
            cout << "Category Discount Information:\n";
            cout << "Category | Discount Rate | Discount Percentage\n";
            cout << "--------------------------------------------\n";
        }

        // 显示所有折扣信息
        while (getline(discountsStream, line)) {
            vector<string> discountData = split(line, ',');
            if (discountData.size() >= 2) {
                string category = discountData[0];
                double rate = stod(discountData[1]);
                int discountPercent = (int)((1.0 - rate) * 100);

                cout << category << " | " << rate << " | ";
                if (discountPercent == 0) {
                    cout << "No discount" << endl;
                } else {
                    cout << discountPercent << "% OFF" << endl;
                }
            }
        }
    } else {
        cout << "Failed to retrieve discount information: " << response << endl;
    }

    cout << "\nPress Enter to return to discount management menu...";
    cin.get();
    manageCategoryDiscountsScreen();
}

// 设置类别折扣界面
void setDiscountScreen() {
    system("cls");
    cout << "\n===== Set Category Discount =====\n";

    cout << "Available product categories:\n";
    cout << "1. Book\n";
    cout << "2. Food\n";
    cout << "3. Clothing\n";
    cout << "4. DailyNecessities\n";
    cout << "5. PersonalCare\n";
    cout << "6. Electronics\n";
    cout << "7. Beverages\n";
    cout << "8. MaternityAndBabyProducts\n";
    cout << "9. Appliances\n";
    cout << "10. StationeryAndSportsGoods\n";
    cout << "11. Toys\n";
    cout << "12. Others\n";
    cout << "0. Return to discount management\n";

    cout << "\nPlease select a category (0-12): ";
    int categoryChoice;
    cin >> categoryChoice;
    cin.ignore();

    if (categoryChoice == 0) {
        manageCategoryDiscountsScreen();
        return;
    }

    vector<string> categories = {
        "Book", "Food", "Clothing", "DailyNecessities", "PersonalCare",
        "Electronics", "Beverages", "MaternityAndBabyProducts", "Appliances",
        "StationeryAndSportsGoods", "Toys", "Others"
    };

    if (categoryChoice < 1 || categoryChoice > 12) {
        cout << "Invalid category selection." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        setDiscountScreen();
        return;
    }

    string selectedCategory = categories[categoryChoice - 1];

    cout << "\nSelected category: " << selectedCategory << endl;
    cout << "Please enter discount rate (0.1-1.0):\n";
    cout << "  1.0 = No discount (100% of original price)\n";
    cout << "  0.9 = 10% discount (90% of original price)\n";
    cout << "  0.8 = 20% discount (80% of original price)\n";
    cout << "  0.5 = 50% discount (50% of original price)\n";
    cout << "Discount rate: ";

    double discountRate;
    cin >> discountRate;
    cin.ignore();

    if (discountRate <= 0 || discountRate > 1.0) {
        cout << "Invalid discount rate. Must be between 0.01 and 1.0." << endl;
        cout << "Press Enter to continue...";
        cin.get();
        setDiscountScreen();
        return;
    }

    // 发送设置折扣请求
    string setRequest = "SET_DISCOUNT " + selectedCategory + " " + to_string(discountRate);
    string response = sendRequest(setRequest);

    if (response == "DISCOUNT_SET") {
        int discountPercent = (int)((1.0 - discountRate) * 100);
        cout << "\nDiscount set successfully!" << endl;
        cout << "Category: " << selectedCategory << endl;
        cout << "Discount rate: " << discountRate << endl;
        if (discountPercent == 0) {
            cout << "Discount: No discount" << endl;
        } else {
            cout << "Discount: " << discountPercent << "% OFF" << endl;
        }
    } else {
        cout << "Failed to set discount: " << response << endl;
    }

    cout << "\nPress Enter to return to discount management menu...";
    cin.get();
    manageCategoryDiscountsScreen();
}
