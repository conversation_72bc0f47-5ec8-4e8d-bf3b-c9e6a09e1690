#define _WIN32_WINNT 0x0600
#define UNICODE
#define _UNICODE
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>
#include <sstream>
#include <iostream>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "comctl32.lib")

using namespace std;

// 窗口控件ID
#define ID_LOGIN_USERNAME 1001
#define ID_LOGIN_PASSWORD 1002
#define ID_LOGIN_BUTTON 1003
#define ID_REGISTER_BUTTON 1004
#define ID_EXIT_BUTTON 1005

#define ID_MAIN_PRODUCTS 2001
#define ID_MAIN_CART 2002
#define ID_MAIN_ORDERS 2003
#define ID_MAIN_BALANCE 2004
#define ID_MAIN_RECHARGE 2005
#define ID_MAIN_LOGOUT 2006

#define ID_PRODUCTS_LIST 3001
#define ID_PRODUCTS_SEARCH 3002
#define ID_PRODUCTS_ADD_CART 3003
#define ID_PRODUCTS_BACK 3004

#define ID_CART_LIST 4001
#define ID_CART_REMOVE 4002
#define ID_CART_GENERATE_ORDER 4003
#define ID_CART_BACK 4004

// 全局变量
HINSTANCE hInst;
HWND hMainWnd = NULL;
HWND hLoginWnd = NULL;
HWND hProductsWnd = NULL;
HWND hCartWnd = NULL;

SOCKET clientSocket = INVALID_SOCKET;
string currentUsername;
string userType;
vector<vector<string>> currentProducts;
vector<vector<string>> currentCart;

// 服务器配置
const string SERVER_IP = "127.0.0.1";
const int PORT = 8888;
const int BUFFER_SIZE = 4096;

// 函数声明
LRESULT CALLBACK LoginWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK MainWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK ProductsWndProc(HWND, UINT, WPARAM, LPARAM);
LRESULT CALLBACK CartWndProc(HWND, UINT, WPARAM, LPARAM);

bool InitializeWinsock();
bool ConnectToServer();
string SendRequest(const string& request);
vector<string> Split(const string& s, char delimiter);
void ShowLoginWindow();
void ShowMainWindow();
void ShowProductsWindow();
void ShowCartWindow();
void LoadProducts();
void LoadCart();
void UpdateProductsList();
void UpdateCartList();

// 初始化Winsock
bool InitializeWinsock() {
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        MessageBox(NULL, L"WSAStartup failed", L"Error", MB_OK | MB_ICONERROR);
        return false;
    }
    return true;
}

// 连接到服务器
bool ConnectToServer() {
    clientSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (clientSocket == INVALID_SOCKET) {
        MessageBox(NULL, L"Socket creation failed", L"Error", MB_OK | MB_ICONERROR);
        return false;
    }

    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(PORT);
    inet_pton(AF_INET, SERVER_IP.c_str(), &serverAddr.sin_addr);

    if (connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        MessageBox(NULL, L"Connection to server failed", L"Error", MB_OK | MB_ICONERROR);
        closesocket(clientSocket);
        clientSocket = INVALID_SOCKET;
        return false;
    }

    return true;
}

// 发送请求到服务器
string SendRequest(const string& request) {
    if (clientSocket == INVALID_SOCKET) {
        return "ERROR_NOT_CONNECTED";
    }

    int result = send(clientSocket, request.c_str(), request.length(), 0);
    if (result == SOCKET_ERROR) {
        return "ERROR_SEND_FAILED";
    }

    char buffer[BUFFER_SIZE];
    memset(buffer, 0, BUFFER_SIZE);
    result = recv(clientSocket, buffer, BUFFER_SIZE, 0);

    if (result <= 0) {
        return "ERROR_RECEIVE_FAILED";
    }

    return string(buffer);
}

// 分割字符串
vector<string> Split(const string& s, char delimiter) {
    vector<string> tokens;
    string token;
    istringstream tokenStream(s);
    while (getline(tokenStream, token, delimiter)) {
        tokens.push_back(token);
    }
    return tokens;
}

// 登录窗口过程
LRESULT CALLBACK LoginWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建用户名标签和输入框
            CreateWindow(L"STATIC", L"Username:", WS_VISIBLE | WS_CHILD,
                50, 50, 100, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
                160, 50, 200, 25, hWnd, (HMENU)ID_LOGIN_USERNAME, hInst, NULL);

            // 创建密码标签和输入框
            CreateWindow(L"STATIC", L"Password:", WS_VISIBLE | WS_CHILD,
                50, 90, 100, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | ES_PASSWORD,
                160, 90, 200, 25, hWnd, (HMENU)ID_LOGIN_PASSWORD, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Login", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 140, 80, 30, hWnd, (HMENU)ID_LOGIN_BUTTON, hInst, NULL);
            CreateWindow(L"BUTTON", L"Register", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                140, 140, 80, 30, hWnd, (HMENU)ID_REGISTER_BUTTON, hInst, NULL);
            CreateWindow(L"BUTTON", L"Exit", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                280, 140, 80, 30, hWnd, (HMENU)ID_EXIT_BUTTON, hInst, NULL);
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_LOGIN_BUTTON:
            {
                // 获取用户名和密码
                wchar_t username[256], password[256];
                GetWindowText(GetDlgItem(hWnd, ID_LOGIN_USERNAME), username, 256);
                GetWindowText(GetDlgItem(hWnd, ID_LOGIN_PASSWORD), password, 256);

                // 转换为string
                char usernameA[256], passwordA[256];
                WideCharToMultiByte(CP_UTF8, 0, username, -1, usernameA, 256, NULL, NULL);
                WideCharToMultiByte(CP_UTF8, 0, password, -1, passwordA, 256, NULL, NULL);

                // 发送登录请求
                string loginRequest = "LOGIN " + string(usernameA) + " " + string(passwordA);
                string response = SendRequest(loginRequest);

                if (response.find("LOGIN_SUCCESS") == 0) {
                    // 解析用户类型
                    vector<string> parts = Split(response, ' ');
                    if (parts.size() >= 2) {
                        currentUsername = usernameA;
                        userType = parts[1];
                        ShowWindow(hWnd, SW_HIDE);
                        ShowMainWindow();
                    }
                } else {
                    MessageBox(hWnd, L"Login failed", L"Error", MB_OK | MB_ICONERROR);
                }
            }
            break;

        case ID_REGISTER_BUTTON:
            MessageBox(hWnd, L"Registration feature not implemented in GUI", L"Info", MB_OK | MB_ICONINFORMATION);
            break;

        case ID_EXIT_BUTTON:
            PostQuitMessage(0);
            break;
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 主窗口过程
LRESULT CALLBACK MainWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建欢迎标签
            CreateWindow(L"STATIC", L"Welcome to Shopping Platform",
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 400, 30, hWnd, NULL, hInst, NULL);

            // 创建功能按钮
            CreateWindow(L"BUTTON", L"Browse Products", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 80, 150, 40, hWnd, (HMENU)ID_MAIN_PRODUCTS, hInst, NULL);
            CreateWindow(L"BUTTON", L"Shopping Cart", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                220, 80, 150, 40, hWnd, (HMENU)ID_MAIN_CART, hInst, NULL);
            CreateWindow(L"BUTTON", L"My Orders", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 140, 150, 40, hWnd, (HMENU)ID_MAIN_ORDERS, hInst, NULL);
            CreateWindow(L"BUTTON", L"Check Balance", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                220, 140, 150, 40, hWnd, (HMENU)ID_MAIN_BALANCE, hInst, NULL);
            CreateWindow(L"BUTTON", L"Recharge", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 200, 150, 40, hWnd, (HMENU)ID_MAIN_RECHARGE, hInst, NULL);
            CreateWindow(L"BUTTON", L"Logout", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                220, 200, 150, 40, hWnd, (HMENU)ID_MAIN_LOGOUT, hInst, NULL);
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_MAIN_PRODUCTS:
            ShowProductsWindow();
            break;
        case ID_MAIN_CART:
            ShowCartWindow();
            break;
        case ID_MAIN_ORDERS:
            MessageBox(hWnd, L"Orders feature not implemented in GUI", L"Info", MB_OK | MB_ICONINFORMATION);
            break;
        case ID_MAIN_BALANCE:
            {
                string response = SendRequest("GET_BALANCE");
                if (response.find("BALANCE") == 0) {
                    string balanceStr = response.substr(8);
                    wstring balanceMsg = L"Current Balance: $" + wstring(balanceStr.begin(), balanceStr.end());
                    MessageBox(hWnd, balanceMsg.c_str(), L"Balance", MB_OK | MB_ICONINFORMATION);
                }
            }
            break;
        case ID_MAIN_RECHARGE:
            MessageBox(hWnd, L"Recharge feature not implemented in GUI", L"Info", MB_OK | MB_ICONINFORMATION);
            break;
        case ID_MAIN_LOGOUT:
            SendRequest("LOGOUT");
            ShowWindow(hWnd, SW_HIDE);
            ShowLoginWindow();
            break;
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 商品浏览窗口过程
LRESULT CALLBACK ProductsWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Products", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 500, 30, hWnd, NULL, hInst, NULL);

            // 创建商品列表框
            CreateWindow(L"LISTBOX", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL,
                50, 60, 500, 300, hWnd, (HMENU)ID_PRODUCTS_LIST, hInst, NULL);

            // 创建搜索框
            CreateWindow(L"STATIC", L"Search:", WS_VISIBLE | WS_CHILD,
                50, 380, 60, 25, hWnd, NULL, hInst, NULL);
            CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
                120, 380, 200, 25, hWnd, (HMENU)ID_PRODUCTS_SEARCH, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Add to Cart", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 420, 100, 30, hWnd, (HMENU)ID_PRODUCTS_ADD_CART, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                450, 420, 100, 30, hWnd, (HMENU)ID_PRODUCTS_BACK, hInst, NULL);

            // 加载商品数据
            LoadProducts();
            UpdateProductsList();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_PRODUCTS_ADD_CART:
            {
                HWND hList = GetDlgItem(hWnd, ID_PRODUCTS_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                if (selection != LB_ERR && selection < currentProducts.size()) {
                    // 简化：添加数量为1的商品到购物车
                    string productName = currentProducts[selection][0];
                    string addRequest = "ADD_TO_CART " + productName + " 1";
                    string response = SendRequest(addRequest);

                    if (response == "CART_UPDATED") {
                        MessageBox(hWnd, L"Item added to cart successfully!", L"Success", MB_OK | MB_ICONINFORMATION);
                    } else {
                        MessageBox(hWnd, L"Failed to add item to cart", L"Error", MB_OK | MB_ICONERROR);
                    }
                }
            }
            break;
        case ID_PRODUCTS_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 购物车窗口过程
LRESULT CALLBACK CartWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_CREATE:
        {
            // 创建标题
            CreateWindow(L"STATIC", L"Shopping Cart", WS_VISIBLE | WS_CHILD | SS_CENTER,
                50, 20, 500, 30, hWnd, NULL, hInst, NULL);

            // 创建购物车列表框
            CreateWindow(L"LISTBOX", L"", WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL,
                50, 60, 500, 300, hWnd, (HMENU)ID_CART_LIST, hInst, NULL);

            // 创建按钮
            CreateWindow(L"BUTTON", L"Remove Item", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                50, 380, 120, 30, hWnd, (HMENU)ID_CART_REMOVE, hInst, NULL);
            CreateWindow(L"BUTTON", L"Generate Order", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                180, 380, 120, 30, hWnd, (HMENU)ID_CART_GENERATE_ORDER, hInst, NULL);
            CreateWindow(L"BUTTON", L"Back", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                430, 380, 120, 30, hWnd, (HMENU)ID_CART_BACK, hInst, NULL);

            // 加载购物车数据
            LoadCart();
            UpdateCartList();
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_CART_REMOVE:
            {
                HWND hList = GetDlgItem(hWnd, ID_CART_LIST);
                int selection = SendMessage(hList, LB_GETCURSEL, 0, 0);
                if (selection != LB_ERR) {
                    string removeRequest = "REMOVE_FROM_CART " + to_string(selection + 1);
                    string response = SendRequest(removeRequest);

                    if (response == "CART_UPDATED") {
                        LoadCart();
                        UpdateCartList();
                        MessageBox(hWnd, L"Item removed from cart!", L"Success", MB_OK | MB_ICONINFORMATION);
                    } else {
                        MessageBox(hWnd, L"Failed to remove item", L"Error", MB_OK | MB_ICONERROR);
                    }
                }
            }
            break;
        case ID_CART_GENERATE_ORDER:
            {
                string generateRequest = "GENERATE_ORDER all";
                string response = SendRequest(generateRequest);

                if (response.find("ORDER_GENERATED") == 0) {
                    MessageBox(hWnd, L"Order generated successfully!", L"Success", MB_OK | MB_ICONINFORMATION);
                    LoadCart();
                    UpdateCartList();
                } else {
                    MessageBox(hWnd, L"Failed to generate order", L"Error", MB_OK | MB_ICONERROR);
                }
            }
            break;
        case ID_CART_BACK:
            ShowWindow(hWnd, SW_HIDE);
            ShowWindow(hMainWnd, SW_SHOW);
            break;
        }
        break;

    case WM_DESTROY:
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// 加载商品数据
void LoadProducts() {
    currentProducts.clear();
    string response = SendRequest("GET_PRODUCTS");

    if (response.find("PRODUCTS") == 0) {
        string productsData = response.substr(9); // 跳过"PRODUCTS "
        istringstream productsStream(productsData);
        string line;

        // 跳过CSV文件头
        if (getline(productsStream, line)) {
            // 文件头不处理
        }

        // 读取所有商品
        while (getline(productsStream, line)) {
            vector<string> productData = Split(line, ',');
            if (productData.size() >= 6) {
                currentProducts.push_back(productData);
            }
        }
    }
}

// 加载购物车数据
void LoadCart() {
    currentCart.clear();
    string response = SendRequest("GET_CART");

    if (response.find("CART") == 0) {
        string cartData = response.substr(5); // 跳过"CART "
        istringstream cartStream(cartData);
        string line;

        // 跳过CSV文件头
        if (getline(cartStream, line)) {
            // 文件头不处理
        }

        // 读取所有购物车项目
        while (getline(cartStream, line)) {
            vector<string> cartData = Split(line, ',');
            if (cartData.size() >= 4) {
                currentCart.push_back(cartData);
            }
        }
    }
}

// 更新商品列表显示
void UpdateProductsList() {
    if (hProductsWnd) {
        HWND hList = GetDlgItem(hProductsWnd, ID_PRODUCTS_LIST);
        SendMessage(hList, LB_RESETCONTENT, 0, 0);

        for (const auto& product : currentProducts) {
            if (product.size() >= 6) {
                string displayText = product[0] + " - $" + product[2] + " (Qty: " + product[3] + ")";
                wstring wDisplayText(displayText.begin(), displayText.end());
                SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
            }
        }
    }
}

// 更新购物车列表显示
void UpdateCartList() {
    if (hCartWnd) {
        HWND hList = GetDlgItem(hCartWnd, ID_CART_LIST);
        SendMessage(hList, LB_RESETCONTENT, 0, 0);

        for (const auto& item : currentCart) {
            if (item.size() >= 4) {
                string displayText = item[0] + " - $" + item[1] + " x " + item[2];
                wstring wDisplayText(displayText.begin(), displayText.end());
                SendMessage(hList, LB_ADDSTRING, 0, (LPARAM)wDisplayText.c_str());
            }
        }
    }
}

// 显示登录窗口
void ShowLoginWindow() {
    if (!hLoginWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = LoginWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"LoginWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        RegisterClass(&wc);

        hLoginWnd = CreateWindow(L"LoginWindow", L"Shopping Platform - Login",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 450, 250,
            NULL, NULL, hInst, NULL);
    }

    ShowWindow(hLoginWnd, SW_SHOW);
    UpdateWindow(hLoginWnd);
}

// 显示主窗口
void ShowMainWindow() {
    if (!hMainWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = MainWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"MainWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        RegisterClass(&wc);

        hMainWnd = CreateWindow(L"MainWindow", L"Shopping Platform - Main Menu",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 500, 320,
            NULL, NULL, hInst, NULL);
    }

    ShowWindow(hMainWnd, SW_SHOW);
    UpdateWindow(hMainWnd);
}

// 显示商品浏览窗口
void ShowProductsWindow() {
    if (!hProductsWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = ProductsWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"ProductsWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        RegisterClass(&wc);

        hProductsWnd = CreateWindow(L"ProductsWindow", L"Shopping Platform - Products",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 650, 520,
            NULL, NULL, hInst, NULL);
    }

    LoadProducts();
    UpdateProductsList();
    ShowWindow(hMainWnd, SW_HIDE);
    ShowWindow(hProductsWnd, SW_SHOW);
    UpdateWindow(hProductsWnd);
}

// 显示购物车窗口
void ShowCartWindow() {
    if (!hCartWnd) {
        WNDCLASS wc = {};
        wc.lpfnWndProc = CartWndProc;
        wc.hInstance = hInst;
        wc.lpszClassName = L"CartWindow";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        RegisterClass(&wc);

        hCartWnd = CreateWindow(L"CartWindow", L"Shopping Platform - Shopping Cart",
            WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
            CW_USEDEFAULT, CW_USEDEFAULT, 650, 480,
            NULL, NULL, hInst, NULL);
    }

    LoadCart();
    UpdateCartList();
    ShowWindow(hMainWnd, SW_HIDE);
    ShowWindow(hCartWnd, SW_SHOW);
    UpdateWindow(hCartWnd);
}

// 程序入口点
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    hInst = hInstance;

    // 初始化Winsock
    if (!InitializeWinsock()) {
        return 1;
    }

    // 连接到服务器
    if (!ConnectToServer()) {
        WSACleanup();
        return 1;
    }

    // 初始化通用控件
    InitCommonControls();

    // 显示登录窗口
    ShowLoginWindow();

    // 消息循环
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // 清理资源
    if (clientSocket != INVALID_SOCKET) {
        closesocket(clientSocket);
    }
    WSACleanup();

    return (int)msg.wParam;
}